import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/airgle/aglDevices/list',
  deleteOne = '/airgle/aglDevices/delete',
  deleteBatch = '/airgle/aglDevices/deleteBatch',
  command = '/airgle/aglDevices/command',
  getDeviceDetail = '/airgle/aglDevices/getDeviceBaseInfo',
  getDeviceLineData = '/airgle/aglDevices/getDeviceLineData',
  getDeviceSignal = '/airgle/aglDevices/getDeviceSignal',
  getCurrentWeather = '/airgle/aglDevices/getCurrentWeather',
  getDevicesStatus = '/airgle/aglDevices/getDevicesStatus',
  getDeviceTimer = '/airgle/aglDevices/getDeviceTimer',
  addDeviceTimer = '/airgle/aglDevices/addDeviceTimer',
}

/**
 * 设备接口
 */
export interface Device {
  id: string;           // 设备ID
  name: string;         // 设备名称
  locationName: string; // 位置名称
  productName: string;  // 产品名称
  icon: string;         // 设备图标
  status: string;       // 设备状态
  lat: string;          // 设备纬度
  lon: string;          // 设备经度
  powerOn?: boolean;    // 电源状态（可选）
  speed?: string;       // 风速设置（可选）
  lock?: boolean;       // 童锁状态（可选）
  uv?: boolean;         // UV状态（可选）
  activeTime?: string;  // 活跃时间（可选）
  category?: string;    // 设备类别（可选）
  createTime?: string;  // 创建时间（可选）
  updateTime?: string;  // 更新时间（可选）
  environment?: string; // 环境（室内/室外）
  isOnline?: boolean;   // 在线状态
  dataPublication?: boolean; // 数据发布状态
  mode?: string;        // 设备模式
}

/**
 * 设备列表请求参数接口
 */
export interface DeviceListParams {
  pageNo: number;
  pageSize: number;
  keyword?: string;
  environment?: string | null;
  isOnline?: boolean | null;
  dataPublication?: boolean | null;
  mode?: string | null;
}

/**
 * 设备列表响应接口
 */
export interface DeviceListResult {
  records: Device[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 设备状态请求参数接口
 */
export interface DeviceStatusParams {
  deviceIds: string[];
}

/**
 * 设备状态信息接口
 */
export interface DeviceStatus {
  deviceId: string;
  powerOn?: boolean;    // 电源状态
  speed?: string;       // 风速
  mode?: string;        // 运行模式
  lock?: boolean;       // 童锁状态
  uv?: boolean;         // UV状态
  [key: string]: any;   // 其他动态属性
}

/**
 * 设备状态响应接口
 */
export interface DeviceStatusResult {
  success: boolean;
  message: string;
  code: number;
  result: DeviceStatus[];
}

/**
 * 定时器信息接口
 */
export interface DeviceTimer {
  date: string;           // 执行定时任务的日期
  functions: any[];       // 定时执行的指令
  enable: boolean;        // 是否已启用定时任务
  timezone_id: string;    // 时区ID，比如 Asia/Shanghai
  loops: string;          // 如果请求失败，返回由 0 和 1 组成的7位数字
  time: string;           // 执行定时任务的时间
  category: string;       // 分类
  timer_id: string;       // 定时任务ID
  timestamp: number;      // 时间戳
}

/**
 * 设备定时器响应接口
 */
export interface DeviceTimerResult {
  success: boolean;
  message: string;
  code: number;
  result: DeviceTimer[];
}

/**
 * 定时器指令接口
 */
export interface TimerFunction {
  code?: string;    // 将要执行指令的 code
  value?: any;      // 将要执行指令的 value，类型为 object
}

/**
 * 添加定时器请求参数接口
 */
export interface AddDeviceTimerParams {
  category?: string;          // 定时任务的分类，如 "category_switch"
  date?: string;              // 执行定时任务的日期，如：20250717
  enable?: boolean;           // 定时器是否启用
  functions?: TimerFunction[]; // 定时执行的指令列表
  loops?: string;             // 由 0 和 1 组成的七位数字
  time?: string;              // 执行定时任务的时间，如16:51
  timezone_id?: string;       // 时区ID，比如 Asia/Shanghai
}

/**
 * 定时器响应数据接口
 */
export interface TimerResponseData {
  category: string;           // 定时任务分类
  date: string;               // 执行日期
  enable: boolean;            // 是否启用
  functions: TimerFunction[]; // 执行指令列表
  loops: string;              // 循环模式
  time: string;               // 执行时间
  timer_id: string;           // 定时器ID
  timezone_id: string;        // 时区ID
}

/**
 * 添加定时器响应接口
 */
export interface AddDeviceTimerResult {
  success: boolean;
  message: string;
  code: number;
  result?: TimerResponseData;
}

/**
 * 获取设备列表
 * @param params - 请求参数
 */
export const getDeviceList = (params: DeviceListParams) =>
  defHttp.get<DeviceListResult>({ url: Api.list, params });

/**
 * 删除单个设备
 * @param id - 设备ID
 */
export const deleteDevice = (id: string) =>
  defHttp.delete({ url: Api.deleteOne, params: { id } }, { joinParamsToUrl: true });

/**
 * 批量删除设备
 * @param ids - 设备ID数组
 */
export const batchDeleteDevices = (ids: string[]) =>
  defHttp.delete({ url: Api.deleteBatch, params: { ids: ids.join(',') } }, { joinParamsToUrl: true });

/**
 * 获取设备详情信息
 * @param deviceId - 设备ID
 * @param deviceType - 设备类型
 */
export const getDeviceDetail = (deviceId: string, deviceType?: string) =>
  defHttp.get({ url: Api.getDeviceDetail, params: { deviceId, deviceType } });

/**
 * 获取设备上报数据折线图
 * @param deviceId - 设备ID
 * @param type - 数据类型 (0-小时1-天 2-月 null-月)
 */
export const getDeviceLineData = (deviceId: string, type?: number) =>
  defHttp.get({ url: Api.getDeviceLineData, params: { deviceId, type } });

/**
 * 获取最新设备信号量
 * @param deviceId - 设备ID
 * @param deviceType - 设备类型
 */
export const getDeviceSignal = (deviceId: string, deviceType?: string) =>
  defHttp.get({ url: Api.getDeviceSignal, params: { deviceId, deviceType } });

/**
 * 获取当前天气信息
 * @param lat - 纬度
 * @param lon - 经度
 */
export const getCurrentWeather = (lat: string, lon: string) =>
  defHttp.get({ url: Api.getCurrentWeather, params: { lat, lon } }); 

/**
 * 设备控制命令
 * @param params - 请求参数
 * @param data - 请求数据
 */
export const command = (params: any, data: any) => {
  return defHttp.post({ url: Api.command, params, data });
};

/**
 * 获取设备状态信息
 * @param deviceIds - 设备ID数组
 */
export const getDevicesStatus = (deviceIds: string[]) =>
  defHttp.post<DeviceStatusResult>({
    url: Api.getDevicesStatus,
    data: deviceIds
  });

/**
 * 获取设备定时器列表
 * @param deviceId - 设备ID
 */
export const getDeviceTimer = (deviceId: string) =>
  defHttp.get<DeviceTimerResult>({
    url: `${Api.getDeviceTimer}/${deviceId}`
  });

/**
 * 添加设备定时器
 * @param deviceId - 设备ID
 * @param params - 定时器参数
 */
export const addDeviceTimer = (deviceId: string, params: AddDeviceTimerParams) =>
  defHttp.post<AddDeviceTimerResult>({
    url: `${Api.addDeviceTimer}/${deviceId}`,
    data: params
  });