# 删除定时API调用修复

## 问题描述
删除定时时提示失败，错误信息：
```
"message": "操作失败，Required request parameter 'timer_ids' for method parameter type List is not present"
```

## 问题分析
根据API文档，删除设备定时的接口 `/airgle/aglDevices/deleteDeviceTimers/{device_id}` 需要一个必需的查询参数：
- `timer_ids`: `array[string]` - 要删除的定时器ID数组

但是原来的实现没有传递这个参数。

## 修复方案

### 1. 修改API定义
更新 `deleteDeviceTimer` 函数以支持 `timer_ids` 参数：

```typescript
// 修改前
export const deleteDeviceTimer = (deviceId: string) =>
  defHttp.delete({ url: `/airgle/aglDevices/deleteDeviceTimers/${deviceId}` });

// 修改后
export const deleteDeviceTimer = (deviceId: string, timerIds: string[]) =>
  defHttp.delete({ 
    url: `/airgle/aglDevices/deleteDeviceTimers/${deviceId}`,
    params: { timer_ids: timerIds }
  });
```

### 2. 修改删除定时方法
更新调用方式，传递定时器ID数组：

```javascript
// 修改前
await deleteDeviceTimer(currentControlDevice.value.id);

// 修改后
await deleteDeviceTimer(currentControlDevice.value.id, [timerId]);
```

### 3. 添加删除确认对话框
为了提升用户体验，添加删除确认对话框：

```javascript
const deleteTimer = async (timerId: string) => {
  // 找到要删除的定时项
  const timerToDelete = timerList.value.find(timer => timer.id === timerId);
  
  // 显示确认对话框
  const confirmed = await new Promise<boolean>((resolve) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除定时 "${timerToDelete.time} ${timerToDelete.repeatLabel}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });
  
  if (!confirmed) return;
  
  // 执行删除
  await deleteDeviceTimer(currentControlDevice.value.id, [timerId]);
};
```

### 4. 添加批量删除功能
支持同时删除多个定时器：

```javascript
const batchDeleteTimers = async (timerIds: string[]) => {
  if (timerIds.length === 0) {
    message.warning('请选择要删除的定时');
    return;
  }
  
  await deleteDeviceTimer(currentControlDevice.value.id, timerIds);
  message.success(`成功删除 ${timerIds.length} 个定时`);
};
```

## 实现的功能

### 1. 单个删除
- 点击定时项的"删除"按钮
- 显示确认对话框，包含定时详情
- 确认后调用API删除
- 本地更新列表并刷新数据

### 2. 批量删除（预留）
- 支持传递多个定时器ID
- 一次API调用删除多个定时器
- 显示删除数量反馈

### 3. 错误处理
- 参数验证：检查设备是否选中
- 数据验证：检查定时项是否存在
- API错误处理：捕获并显示错误信息
- 日志记录：记录删除操作的详细信息

## API调用格式

### 请求
```
DELETE /airgle/aglDevices/deleteDeviceTimers/{device_id}?timer_ids=timer1&timer_ids=timer2
```

### 参数
- **Path参数**：
  - `device_id`: 设备ID
- **Query参数**：
  - `timer_ids`: 定时器ID数组（必需）

### 响应
```json
{
  "success": true,
  "message": "删除成功",
  "code": 200
}
```

## 测试验证

### 1. 单个删除测试
1. 选择一个设备
2. 点击某个定时的"删除"按钮
3. 确认删除对话框显示正确的定时信息
4. 点击"确定"
5. 检查API调用是否包含正确的参数
6. 验证定时是否从列表中移除

### 2. 参数验证测试
- 未选择设备时删除：应显示"未选择设备"错误
- 删除不存在的定时：应显示"定时项不存在"错误

### 3. API调用验证
检查网络面板中的API请求：
- URL应该包含正确的设备ID
- Query参数应该包含 `timer_ids`
- 参数值应该是要删除的定时器ID

## 注意事项

1. **参数格式**：`timer_ids` 是数组类型，即使删除单个定时器也要传递数组
2. **确认对话框**：删除操作不可逆，必须有确认步骤
3. **数据同步**：删除后需要刷新列表确保数据一致性
4. **错误处理**：API调用失败时要有适当的错误提示
5. **日志记录**：记录删除操作便于调试

## 后续优化

1. **批量选择**：添加复选框支持批量选择删除
2. **撤销功能**：删除后提供撤销选项
3. **删除动画**：添加删除时的过渡动画
4. **权限控制**：根据用户权限控制删除功能的可用性

现在删除定时功能应该能正常工作了！
