import { defHttp } from '/@/utils/http/axios';

enum Api {
  getDeviceTypeList = '/airgle/aglDevices/getDeviceTypeList',
  exportDataAnalytics = '/airgle/aglDevices/exportDataAnalytics',
}

/**
 * 空气质量参数接口
 */
export interface AirQuality {
  pollutionLevel: string[];
  airFilterTime: string;
}

/**
 * 数据分析导出请求参数接口
 */
export interface ExportDataAnalyticsParams {
  format: string;
  deviceTypes: string[];
  filterLifetime?: string;
  airQuality?: AirQuality;
  activityFilterTime?: string;
  deviceUserTyoe?: string;
  providerIds?: (string | number)[];
  deviceSalerIds?: (string | number)[];
}

/**
 * 获取设备型号列表
 */
export const getDeviceTypeList = () =>
  defHttp.get<string[]>({ url: Api.getDeviceTypeList });

/**
 * 导出数据分析报告
 * @param params - 导出参数
 */
export const exportDataAnalytics = (params: ExportDataAnalyticsParams) =>
  defHttp.post(
    { 
      url: Api.exportDataAnalytics, 
      data: params,
      responseType: 'blob'
    }, 
    { isTransformResponse: false }
  ); 