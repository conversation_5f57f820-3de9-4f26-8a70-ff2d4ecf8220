<template>
  <div class="count">
    <div class="head justify-between">
      <div class="group_36 flex-col">
        <span class="text_63">Device&nbsp;Management</span>
        <div class="text-wrapper_13">
          <span class="text_64">Device&nbsp;Management&nbsp;&nbsp;&nbsp;&gt;&nbsp;&nbsp;</span>
          <span class="text_65">{{ deviceInfo.name || deviceInfo.deviceName }}</span>
        </div>
        <div class="group_37 flex-row">
          <div class="image-wrapper_4 flex-col">

            <img class="label_8" referrerpolicy="no-referrer" :src="`https://images.tuyacn.com/${deviceInfo.icon}`"
              alt="Device" />
          </div>
          <div class="section_7 flex-col justify-between">
            <span class="text_66">{{ deviceInfo.locationName }}</span>
            <div class="block_20 flex-row">
              <img class="thumbnail_8" referrerpolicy="no-referrer"
                src="https://lanhu-oss-2537-2.lanhuapp.com/6f33348048c33823ed3948bb733b8ac5" />
              <span class="text_67 display-center">Indoor</span>
            </div>
          </div>
          <div class="section_8 flex-row">
            <img class="thumbnail_9" referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/fcb2ce7dde63149c158e77b995159351" />
            <span class="text_68 display-center">Admin</span>
          </div>
          <span class="text_69">Last&nbsp;update&nbsp;&nbsp;{{ formatTime(deviceInfo.timestamp) }}</span>
        </div>
      </div>
      <div class="head-center">
        <div class="box_12 flex-col infobg">
          <div class="pen" @click="toggleDeviceInfo"></div>
          <div class="group_39 flex-row">
            <img class="image_8" referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/2f1a691132fe6e63e5c230ea91700383" />
            <span class="text_70">TV screen view</span>
          </div>
          <div class="group_40 flex-row device-info-container">

            <!-- 默认状态：只显示图片，居中 -->
            <div v-if="!isDeviceInfoHovered" class="image-wrapper_5 flex-col image-only">
              <img class="image_9" referrerpolicy="no-referrer"
                src="https://lanhu-oss-2537-2.lanhuapp.com/acbe40e1991bdf35a802465775ad227e" />
            </div>

            <!-- 悬停状态：显示完整信息 -->
            <template v-else>
              <div class="image-wrapper_5 flex-col">
                <img class="image_9" referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/acbe40e1991bdf35a802465775ad227e" />
              </div>
              <div class="paragraph_7 device-details">
                <div class="flex-align">
                  <span> Virtual &nbsp; ID:{{ deviceInfo.id }} </span>
                  <img v-if="deviceInfo.id" @click="copyToClipboard(deviceInfo.id)" class="thumbnail_12 thumbnail-icn"
                    referrerpolicy="no-referrer"
                    src="https://lanhu-oss-2537-2.lanhuapp.com/139cfb2a03824ac5b044e272c9cfe68a" />
                </div>
                <div class="flex-align">
                  <span> IP: &nbsp;{{ deviceInfo.ip }}</span>
                </div>
                <div class="flex-align">
                  <span> Mac: &nbsp;{{ deviceInfo.mac }} </span>
                </div>
                <div class="flex-align">
                  <span> Time&nbsp; Zone:&nbsp; {{ deviceInfo.timeZone }}</span>
                </div>
                <div class="flex-align">
                  <span> Signal&nbsp; Strength: &nbsp;{{ deviceInfo.signalStrength }} </span>
                  <img v-if="deviceInfo.signalStrength" class="thumbnail_11 thumbnail-icn" referrerpolicy="no-referrer"
                    @click="handleGetSignal"
                    src="https://lanhu-oss-2537-2.lanhuapp.com/f3b206867422148642215e047cb60338" />
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="head-right">
        <a-button @click="handleAdd"
          style="background-color: rgba(0, 160, 233, 1); color: #fff; width: 180px; height: 50px; font-size: 20px"
          size="large">
          <template #icon>
            <DownloadOutlined />
          </template>
          Publish date
        </a-button>
        <a-button @click="openControlModal(deviceInfo)"
          style="background-color: #fff; color: #000; width: 180px; height: 50px; font-size: 20px; margin-top: 20px"
          size="large">
          <template #icon>
            <DownloadOutlined />
          </template>
          Open Control
        </a-button>
      </div>
    </div>
    <div class="center justify-between">
      <div class="center-left"> Air quality overview </div>
      <div class="center-right flex-align">
        <div class="switch-wrapper">
          legend &nbsp;&nbsp;
          <a-switch v-model:checked="switChecked" />
        </div>
        <a-radio-group v-model:value="filterValue" button-style="solid">
          <a-radio-button value="a">Minutes</a-radio-button>
          <a-radio-button value="b">Hours</a-radio-button>
          <a-radio-button value="c">Days</a-radio-button>
          <a-radio-button value="d">Months</a-radio-button>
        </a-radio-group>
      </div>
    </div>
    <div class="downFrame">
      <div class="down">
        <div class="group_12 flex-align">
          <span class="text_12">Last&nbsp;24&nbsp;hours</span>
          <img class="thumbnail_5" referrerpolicy="no-referrer"
            src="https://lanhu-oss-2537-2.lanhuapp.com/195a52fc16cabf3eb1f1c4807803df06" />
          <span v-for="(item, index) in 24" :key="index" class="text_1">{{ item < 10 ? '0' + item : item }}{{ item == 24
            ? '(h)' : '' }}</span>
              <img class="thumbnail_6" referrerpolicy="no-referrer"
                src="https://lanhu-oss-2537-2.lanhuapp.com/c7071a054b46d32dfdbf3018d3e5e154" />
              <img class="thumbnail_7" referrerpolicy="no-referrer"
                src="https://lanhu-oss-2537-2.lanhuapp.com/9101d81c6822cd13e280c900bc1e7198" />
        </div>
      </div>
      <div v-if="isAG2000" class="line-chart-div">
        <div class="line-chart-box">
          <threelineChart :title="'PM2.5 (μg/m³)'" :outdoorData="[130, 58, 54, 50, 48, 47, 50, 52, 49]"
            :indoorData="[140, 20, 10, 5, 4, 3, 3, 3, 3]"
            :xAxisData="['00', '01', '02', '03', '04', '05', '06', '07', '08']" />
        </div>
        <div class="line-chart-box">
          <threelineChart :title="'CO2 (ppm)'" :outdoorData="[130, 58, 54, 50, 48, 47, 50, 52, 49]"
            :indoorData="[140, 20, 10, 5, 4, 3, 3, 3, 3]"
            :xAxisData="['00', '01', '02', '03', '04', '05', '06', '07', '08']" />
        </div>
      </div>
      <div v-else=""></div>
    </div>
  </div>

  <!-- 设备控制弹窗 -->
  <a-modal v-model:open="controlModalVisible" :title="null" :footer="null" :closable="false" width="400px" centered
    :bodyStyle="{ padding: 0 }" wrapClassName="device-control-modal">
    <div class="control-modal-container">
      <!-- 头部区域 -->
      <div class="control-header">
        <div class="header-title">{{ currentControlDevice?.name || 'Device' }}</div>
        <div class="header-right" @click="closeControlModal">
          <CloseOutlined style="color: white; font-size: 18px;" />
        </div>
      </div>

      <!-- 控制内容区域 -->
      <div class="control-content">
        <!-- 中心电源按钮 -->
        <div class="power-section">
          <div class="power-button" :class="{ 'power-on': currentControlDevice?.powerOn }" @click="toggleDevicePower">
            <PoweroffOutlined style="font-size: 48px;" />
          </div>
        </div>

        <!-- 控制选项列表 -->
        <div class="control-options">
          <!-- 童锁 选项 -->
          <div class="control-option">
            <span class="option-label">Lock</span>
            <a-switch v-model:checked="isLockOn" @change="handleLockChange" />
          </div>

          <!-- UV 选项 -->
          <div class="control-option">
            <span class="option-label">UV</span>
            <a-switch v-model:checked="titaniumProUV" @change="handleTitaniumUVChange" />
          </div>

          <!-- Speed 选项 -->
          <div class="control-option">
            <span class="option-label">Speed</span>
            <a-select v-model:value="selectedWindSpeed" style="width: 80px" @change="handleWindSpeedChange"
              size="small">
              <a-select-option v-for="item in WIND_SPEED_OPTIONS" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

import { DownloadOutlined, PoweroffOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { getDeviceDetail, getDeviceLineData, getDeviceSignal, command } from './api/deviceManagement.api';

import threelineChart from './components/echartsComponent/threelineChart.vue';

// 类型定义
interface DeviceInfo {
  id: string;
  deviceName: string;
  name: string;
  ip: string;
  mac: string;
  timeZone: string;
  signalStrength: string;
  timestamp: number;
  icon: string;
  locationName: string;
  powerOn?: boolean;
}

interface ChartData {
  properties?: {
    pm25_outdoor?: number[];
    pm25_indoor?: number[];
    co2_outdoor?: number[];
    co2_indoor?: number[];
  };
}

interface SignalResponse {
  signalLevel: string;
  indicatorType: string;
  eventTime: number;
  signal: number;
}

// 常量定义
const TIME_RANGE_MAP = {
  a: 1, // minutes
  b: 2, // hours
  c: 3, // days
  d: 4  // months
} as const;

const DEFAULT_DEVICE_INFO: DeviceInfo = {
  id: '',
  deviceName: '',
  name: '',
  ip: '',
  mac: '',
  timeZone: '',
  signalStrength: '',
  timestamp: 0,
  icon: '',
  locationName: ''
};

const WIND_SPEED_OPTIONS = [
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
];

// 响应式数据
const switChecked = ref(false);
const filterValue = ref('a');
const loading = ref(false);
const isAG2000 = ref(true);
const deviceInfo = ref<DeviceInfo>(DEFAULT_DEVICE_INFO);
const deviceSignal = ref<Record<string, any>>({});
const chartData = ref<ChartData>({});
const pm25Values = ref({ outdoor: 0, indoor: 0 });
const co2Values = ref({ outdoor: 0, indoor: 0 });

// 控制相关状态
const controlModalVisible = ref(false);
const currentControlDevice = ref<DeviceInfo | null>(null);
const titaniumProUV = ref(false);
const selectedWindSpeed = ref('1');
const isLockOn = ref(false);

// 路由相关
const route = useRoute();
const router = useRouter();

// 设备信息显示状态
const isDeviceInfoHovered = ref(false);

// 切换设备信息显示状态
const toggleDeviceInfo = () => {
  isDeviceInfoHovered.value = !isDeviceInfoHovered.value;
};

// 计算属性
const getTimeRangeType = computed(() => TIME_RANGE_MAP[filterValue.value as keyof typeof TIME_RANGE_MAP] || 2);

// 方法定义
const formatTime = (timestamp?: number): string => {
  if (!timestamp) return '13:00';
  const date = new Date(timestamp);
  return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  } catch (err) {
    // 降级处理
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      message.success('已复制到剪贴板');
    } catch (err) {
      message.error('复制失败');
    }
    document.body.removeChild(textarea);
  }
};

// 设备控制相关方法
const sendDeviceCommand = async (deviceId: string, commandData: { code: string; value: any }[]) => {
  try {
    await command({ id: deviceId }, commandData);
    return true;
  } catch (error) {
    console.error('设备控制失败:', error);
    message.error('设备控制失败');
    return false;
  }
};

const toggleDevicePower = async () => {
  if (!currentControlDevice.value?.id) return;

  const success = await sendDeviceCommand(
    currentControlDevice.value.id,
    [{ code: 'switch', value: !currentControlDevice.value.powerOn }]
  );

  if (success && currentControlDevice.value) {
    currentControlDevice.value.powerOn = !currentControlDevice.value.powerOn;
  }
};

const handleWindSpeedChange = async (value: string) => {
  if (!currentControlDevice.value?.id) return;
  await sendDeviceCommand(currentControlDevice.value.id, [{ code: 'speed', value }]);
};

const handleLockChange = async (value: boolean) => {
  if (!currentControlDevice.value?.id) return;
  const success = await sendDeviceCommand(currentControlDevice.value.id, [{ code: 'lock', value }]);
  if (success) {
    isLockOn.value = value;
  }
};

const handleTitaniumUVChange = async (checked: boolean) => {
  if (!currentControlDevice.value?.id) return;
  await sendDeviceCommand(currentControlDevice.value.id, [{ code: 'uv', value: checked }]);
};

const handleGetSignal = async () => {
  try {
    const deviceId = route.query.id as string;
    if (!deviceId) {
      message.error('设备ID不存在');
      return;
    }

    const response: SignalResponse = await getDeviceSignal(deviceId, 'WiFi');
    console.log('Signal Response:', response);
    if (response && response.signal !== undefined) {
      deviceInfo.value.signalStrength = `${response.signal}dBm`;
      message.success('信号强度已更新');
    } else {
      message.error('获取信号强度失败');
    }
  } catch (error) {
    console.error('获取信号强度失败:', error);
    message.error('获取信号强度失败');
  }
};

const handleAdd = () => {
  console.log('Publish date clicked');
};

const openControlModal = (device: DeviceInfo) => {
  currentControlDevice.value = device;
  controlModalVisible.value = true;
};

const closeControlModal = () => {
  controlModalVisible.value = false;
  currentControlDevice.value = null;
};

// 数据获取
const fetchDeviceDetail = async () => {
  try {
    loading.value = true;
    const deviceId = route.query.id as string;
    const deviceType = route.query.deviceType as string;

    if (!deviceId) {
      message.error('设备ID不存在');
      router.push('/deviceManagement');
      return;
    }

    // 并行获取设备详情和图表数据
    const [detailResult, chartResult] = await Promise.allSettled([
      getDeviceDetail(deviceId, deviceType),
      getDeviceLineData(deviceId, getTimeRangeType.value)
    ]);

    // 处理设备基本信息
    if (detailResult.status === 'fulfilled' && detailResult.value) {
      const detailData = detailResult.value.result || detailResult.value;

      // 从路由和API响应中组装设备信息
      deviceInfo.value = {
        id: detailData?.id || deviceId,
        deviceName: route.query.productName as string || detailData?.productName,
        name: route.query.name as string || detailData?.name || detailData?.id,
        ip: detailData?.ip || detailData?.IP || '',
        mac: detailData?.mac || detailData?.MAC || '',
        timeZone: detailData?.timeZone || detailData?.timezone || detailData?.TimeZone || '',
        signalStrength: route.query.signalStrength as string || detailData?.signalStrength || detailData?.signal || '',
        timestamp: detailData?.timestamp || detailData?.eventTime || Date.now(),
        icon: route.query.icon as string || detailData?.icon || '',
        locationName: route.query.locationName as string || detailData?.locationName || 'Living Room'
      };
    }

    // 处理图表数据
    if (chartResult.status === 'fulfilled' && chartResult.value) {
      const chartDataValue = chartResult.value.result || chartResult.value;
      chartData.value = chartDataValue;

      if (chartDataValue.properties) {
        const { properties } = chartDataValue;

        // 更新PM2.5数值
        pm25Values.value = {
          outdoor: properties.pm25_outdoor?.[properties.pm25_outdoor.length - 1] || 50,
          indoor: properties.pm25_indoor?.[properties.pm25_indoor.length - 1] || 3
        };

        // 更新CO2数值
        co2Values.value = {
          outdoor: properties.co2_outdoor?.[properties.co2_outdoor.length - 1] || 400,
          indoor: properties.co2_indoor?.[properties.co2_indoor.length - 1] || 500
        };
      }
    }

  } catch (error) {
    console.error('获取设备详情失败:', error);
    message.error('获取设备详情失败');
  } finally {
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  fetchDeviceDetail();
});
</script>
<style lang="less" scoped>
.count {
  padding: 50px;
  box-sizing: border-box;

  .head {
    box-sizing: border-box;

    .group_36 {
      width: 404px;
      height: 155px;
    }

    .text_63 {
      width: 344px;
      height: 33px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 36px;
      font-family: Arial-BoldMT;
      font-weight: 700;
      text-align: left;
      white-space: nowrap;
      line-height: 36px;
    }

    .text-wrapper_13 {
      width: 241px;
      height: 15px;
      overflow-wrap: break-word;
      font-size: 0;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 16px;
      margin: 33px 0 0 1px;
    }

    .text_64 {
      width: 241px;
      height: 15px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 16px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 16px;
    }

    .text_65 {
      width: 241px;
      height: 15px;
      overflow-wrap: break-word;
      color: rgba(0, 160, 233, 1);
      font-size: 16px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 16px;
    }

    .group_37 {
      width: 403px;
      height: 61px;
      margin: 13px 0 0 1px;
    }

    .image-wrapper_4 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 3px;
      height: 61px;
      border: 1px solid rgba(208, 213, 221, 1);
      width: 61px;
    }

    .label_8 {
      width: 90%;
      margin: 4px auto;
    }

    .section_7 {
      width: 101px;
      height: 53px;
      margin: 8px 0 0 26px;
    }

    .text_66 {
      width: 99px;
      height: 17px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 18px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 18px;
      margin-left: 2px;
    }

    .block_20 {
      background-color: rgba(225, 243, 253, 1);
      border-radius: 10px;
      width: 80px;
      height: 20px;
      margin-top: 16px;
    }

    .thumbnail_8 {
      width: 14px;
      height: 12px;
      margin: 4px 0 0 9px;
    }

    .text_67 {
      width: 40px;
      height: 10px;
      overflow-wrap: break-word;
      color: rgba(0, 160, 233, 1);
      font-size: 14px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 14px;
      margin: 5px 11px 0 6px;
    }

    .section_8 {
      background-color: rgba(225, 243, 253, 1);
      border-radius: 10px;
      width: 80px;
      height: 20px;
      margin-top: 41px;
    }

    .thumbnail_9 {
      width: 10px;
      height: 12px;
      margin: 4px 0 0 7px;
    }

    .text_68 {
      width: 40px;
      height: 10px;
      overflow-wrap: break-word;
      color: rgba(0, 160, 233, 1);
      font-size: 14px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 14px;
      margin: 5px 15px 0 8px;
    }

    .text_69 {
      width: 115px;
      height: 14px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 14px;
      margin: 44px 0 0 20px;
    }
  }

  .box_12 {
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.2);
    background-color: #fff;
    border-radius: 6px;
    width: 659px;
    height: 207px;
    position: relative;

    .pen {
      position: absolute;
      right: 30px;
      top: 50px;
      width: 25px;
      height: 22px;
      cursor: pointer;
      background: url('/src/assets/images/pen.png') center center no-repeat;

    }
  }

  .infobg {
    background: url('/src/assets/images/info_bg.png') center bottom no-repeat #fff;
  }

  .group_39 {
    background-color: #00a0e9;
    border-radius: 6px 6px 0 0;
    width: 659px;
    height: 36px;
    justify-content: center;
    align-items: center;
  }

  .image_8 {
    width: 22px;
    margin-right: 5px;
  }

  .text_70 {
    color: #fff;
    font-size: 16px;
    font-family: ArialMT;
    font-weight: normal;
  }

  .group_40 {
    height: 115px;
    margin: 28px auto;
    /* 使用auto实现水平居中 */
    width: 100%;
    /* 使用全宽 */
    max-width: 600px;
    /* 设置最大宽度避免过宽 */
    min-width: 400px;
    /* 设置最小宽度 */
  }

  /* 设备信息容器 */
  .device-info-container {
    position: relative;
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 默认状态：只显示图片，居中 */
  .device-info-container .image-only {
    margin: 0 auto;
    /* 水平居中 */
  }

  /* 悬停状态：左右布局 */
  .device-info-container .device-details {
    max-width: 270px;
    margin-left: 20px;
    flex: 1;
  }



  .image-wrapper_5 {
    background: url(https://lanhu-oss-2537-2.lanhuapp.com/05815cac1e4f53de2ab10723275f6de5) center center no-repeat;
    background-size: contain;
    width: 157px;
    height: 115px;
    transition: all 0.3s ease;
    flex-shrink: 0;
    /* 防止图片容器被压缩 */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image_9 {
    width: 59px;
    height: 35px;
    margin: 0;
    /* 移除margin，让flex布局自动居中 */
  }

  .paragraph_7 {
    height: auto;
    /* 改为自动高度 */
    min-height: 110px;
    /* 设置最小高度 */
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-family: ArialMT;
    font-weight: normal;
    text-align: left;
    line-height: 24px;
    margin: 1px 0 0 0;
    white-space: normal;
    /* 允许文本换行 */
    word-break: break-word;
    /* 长单词可以换行 */
    flex: 1;
    /* 占用剩余空间 */
  }

  /* 设备信息每行的样式 */
  .flex-align {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    min-height: 20px;
    flex-wrap: wrap;
    /* 允许换行 */
  }

  .flex-align span {
    flex: 1;
    min-width: 0;
    /* 允许文本收缩 */
    word-break: break-all;
    /* 强制长文本换行 */
  }

  .thumbnail_10 {
    width: 15px;
    height: 11px;
  }

  .thumbnail_11 {
    width: 11px;
    height: 11px;
  }

  .thumbnail_12 {
    width: 15px;
    height: 15px;
  }

  .thumbnail-icn {
    margin-left: 10px;
  }

  .head-right {
    display: flex;
    flex-direction: column;
  }

  .center {
    margin-top: 15px;

    .center-left {
      font-family: Arial;
      font-weight: bold;
      font-size: 24px;
      color: #000000;
    }

    .center-right {
      .switch-wrapper {
        font-family: Arial;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        margin-right: 20px;
      }
    }
  }

  .downFrame {
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    background: #FFFFFF;
  }

  .down {
    margin-top: 26px;

    .group_12 {
      padding: 0 30px;
      box-sizing: border-box;
      background-color: rgba(245, 249, 254, 1);
      border-radius: 6px;
      position: relative;
      width: 100%;
      height: 48px;
      border: 1px solid rgba(230, 230, 230, 1);
    }

    .text_1 {
      width: 96px;
      overflow-wrap: break-word;
      color: #969dac;
      font-size: 14px;
      font-family: ArialMT;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      margin: 0 0 0 28px;
    }

    .thumbnail_5 {
      width: 6px;
      height: 11px;
      margin: 0 0 0 63px;
    }

    .thumbnail_6 {
      width: 6px;
      height: 10px;
      margin: 0 0 0 23px;
    }

    .thumbnail_7 {
      position: absolute;
      left: 416px;
      top: 42px;
      width: 11px;
      height: 9px;
    }
  }

  .line-chart-div {
    .line-chart-box {
      border-bottom: 1px solid #e6e6e6;
    }

    // &:last-child {
    //   .line-chart-box {
    //     border-bottom: none;
    //   }
    // }
  }
}

/* 设备控制弹窗样式 */
:deep(.device-control-modal .ant-modal-content) {
  border-radius: 20px;
  overflow: hidden;
}

.control-modal-container {
  width: 100%;
  background: #f5f5f5;
}

.control-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  cursor: pointer;
  min-width: 18px;
}

.header-left {
  justify-content: flex-start;
}

.header-right {
  justify-content: flex-end;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  flex: 1;
}

.control-content {
  padding: 30px 20px;
  background: #f5f5f5;
  min-height: 400px;
}

.power-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.power-button {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
  border: 4px solid #d9d9d9;
}

.power-button:hover {
  background: #d9d9d9;
}

.power-button.power-on {
  background: #52c41a;
  color: white;
  border-color: #52c41a;
  box-shadow: 0 0 20px rgba(82, 196, 26, 0.3);
}

.control-options {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-option:last-child {
  border-bottom: none;
}

.control-option:hover {
  background-color: #fafafa;
}

.option-label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}
</style>
