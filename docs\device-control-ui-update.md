# 设备控制界面UI更新

## 更新内容

### 1. 添加Tab切换功能
在电源控制按钮上方添加了Data和Control两个tab切换：

#### 功能特性：
- **Data Tab**：点击时跳转到设备详情页面
- **Control Tab**：默认激活，显示设备控制选项
- **视觉反馈**：激活状态有颜色变化和图标高亮

#### 路由跳转：
```javascript
// 点击Data时执行
router.push({ 
  path: '/deviceManagement/detail',
  query: {
    id: device.id,
    deviceType: 'WiFi'
  }
});
```

### 2. 控制选项间距调整
将下方控制选项的间距设置为16px：

#### 样式变化：
- **原来**：选项在一个白色容器内，用边框分隔
- **现在**：每个选项独立的白色卡片，间距16px
- **视觉效果**：更清晰的分层和更好的视觉分离

### 3. 控制选项内容更新
根据图片要求更新了选项内容：

#### 选项列表：
1. **Mode** - 模式设置（带右箭头）
2. **Speed** - 速度设置（带右箭头）
3. **Titanium Pro UV** - UV功能开关
4. **Cloud Timing** - 云定时设置（带右箭头）

## 技术实现

### 1. 状态管理
```javascript
const activeTab = ref('Control');  // 当前激活的tab

const switchTab = (tab: string) => {
  activeTab.value = tab;
  if (tab === 'Data' && currentControlDevice.value) {
    // 跳转到设备详情页
    router.push({ 
      path: '/deviceManagement/detail',
      query: {
        id: currentControlDevice.value.id,
        deviceType: 'WiFi'
      }
    });
    closeControlModal();
  }
};
```

### 2. HTML结构
```html
<!-- Tab切换区域 -->
<div class="tab-section">
  <div class="tab-container">
    <div class="tab-item" :class="{ active: activeTab === 'Data' }" @click="switchTab('Data')">
      <div class="tab-icon"><!-- SVG图标 --></div>
      <span class="tab-label">Data</span>
    </div>
    <div class="tab-item" :class="{ active: activeTab === 'Control' }" @click="switchTab('Control')">
      <div class="tab-icon"><!-- SVG图标 --></div>
      <span class="tab-label">Control</span>
    </div>
  </div>
</div>

<!-- 控制选项列表 -->
<div class="control-options">
  <div class="control-option">
    <span class="option-label">Mode</span>
    <RightOutlined style="font-size: 18px; color: #999;" />
  </div>
  <!-- 其他选项... -->
</div>
```

### 3. CSS样式
```css
/* Tab切换样式 */
.tab-container {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.tab-item.active {
  opacity: 1;
}

.tab-item.active .tab-icon {
  color: #1890ff;
}

.tab-item.active .tab-label {
  color: #1890ff;
}

/* 控制选项样式 */
.control-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.control-option:last-child {
  margin-bottom: 0;
}
```

## 视觉效果

### 1. Tab切换
- **未激活状态**：灰色图标和文字，透明度60%
- **激活状态**：蓝色图标和文字，透明度100%
- **悬停效果**：透明度80%

### 2. 控制选项
- **独立卡片**：每个选项都是独立的白色卡片
- **16px间距**：选项之间有16px的间距
- **阴影效果**：轻微的阴影增加层次感
- **圆角设计**：8px圆角，现代化外观

### 3. 交互反馈
- **点击Data**：立即跳转到设备详情页并关闭弹窗
- **点击Control**：切换到控制tab（默认状态）
- **悬停效果**：选项有背景色变化

## 使用说明

### 1. 打开设备控制弹窗
点击设备列表中的控制图标，弹窗默认显示Control tab。

### 2. 切换到数据查看
点击弹窗顶部的"Data" tab，会自动跳转到设备详情页面。

### 3. 控制设备功能
在Control tab中：
- 点击中央电源按钮控制设备开关
- 点击"Titanium Pro UV"开关控制UV功能
- 点击其他选项（Mode、Speed、Cloud Timing）进入相应设置

## 注意事项

1. **路由跳转**：确保 `/deviceManagement/detail` 路由已正确配置
2. **设备ID传递**：跳转时会传递当前设备的ID和设备类型
3. **状态重置**：关闭弹窗时会重置tab状态为Control
4. **响应式设计**：样式适配不同屏幕尺寸

## 后续优化建议

1. **图标优化**：可以使用更符合品牌风格的自定义图标
2. **动画效果**：添加tab切换和选项展开的动画
3. **加载状态**：在跳转时显示加载指示器
4. **错误处理**：添加路由跳转失败的错误处理
