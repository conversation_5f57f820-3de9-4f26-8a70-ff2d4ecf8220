<!--
  设备管理列表组件
  功能：展示设备列表，支持分页、批量删除、设备电源控制等操作
  作者：开发团队
  创建时间：2024
-->
<template>
  <!-- 主容器卡片 -->
  <a-card style="width: 100%;height: 100%;">
    <!-- 设备列表表格 -->
    <a-table :columns="columns" :data-source="devices" row-key="id" :pagination="false" :row-selection="rowSelection"
      :loading="loading" :row-class-name="getRowClassName"
      :scroll="{ y: '60vh' }">
      <!-- 表头自定义渲染 -->
      <template #headerCell="{ column }">
        <template v-if="column.key === 'power'">
          <span class="flex-align">
            <!-- 批量删除按钮 -->
            <div class="delete-icn" @click="handleDelete">
              <DeleteOutlined style="color: red;font-size: 24px;" />
            </div>
          </span>
        </template>
      </template>
      <!-- 单元格自定义渲染 -->
      <template #bodyCell="{ column, record }">
        <!-- 设备名称列自定义渲染 -->
        <template v-if="column.dataIndex === 'name'">
          <div class="flex items-center gap-2 device-row"
            :class="{ 'device-row-selected': selectedDeviceId === record.id }" @click="handleDeviceClick(record)"
            @dblclick="handleDeviceDoubleClick(record)">
            <!-- 设备图标 -->
            <img alt="productImg" width="39px" :src="`https://images.tuyacn.com/${record.icon}`" />
            <div>
              <!-- 设备名称（双击可查看详情） -->
              <div class="device-name">{{ record.name }}</div>
              <!-- 产品名称 -->
              <div class="text-gray-400 text-xs">{{ record.productName }}</div>
              <!-- 位置名称 -->
              <div class="text-gray-400 text-xs">{{ record.locationName }}</div>
            </div>
          </div>
        </template>
        <!-- 电源控制列自定义渲染 -->
        <template v-else-if="column.dataIndex === 'power'">
          <div class="controlPower">
            <!-- 控制图标 -->
            <div @click="handleControl(record)" class="control-div display-center">
              <img src="@/assets/device/control.png" class="control-img" alt="" />
            </div>
            <!-- 电源开关按钮 -->
            <Button shape="circle" style="width: 44px;height: 44px;flex-shrink: 0;"
              :class="record.powerOn ? 'power-btn' : ''" @click="togglePower(record)">
              <template #icon>
                <PoweroffOutlined />
              </template>
            </Button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="footer-pagination">
      <!-- 分页控制区域 -->
      <div class="selectPage display-between">
        <!-- 每页显示数量选择 -->
        <div class="flex-align">
          Show：
          <a-select v-model:value="pageSize" style="width: 70px" @change="handlePageSizeChange">
            <a-select-option :value="20">20</a-select-option>
            <a-select-option :value="50">50</a-select-option>
          </a-select>
        </div>
        <!-- 跳转到指定页面 -->
        <div class="flex-align"> Jump to page： <a-input v-model:value="pageInput" style="width: 70px" placeholder="Enter"
            @pressEnter="handleJumpToPage" /> </div>
      </div>
  
      <!-- 分页信息和分页器 -->
      <div class="display-between" style="padding-bottom: 10px">
        <div></div>
        <div class="flex-align">
          <!-- 分页信息显示 -->
          {{ `${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, total)}` }} of {{ total }} items
          <!-- 分页组件 -->
          <Pagination style="margin-left: 15px" v-model:current="currentPage" :page-size="pageSize" :total="total"
            :show-size-changer="false" @change="onPageChange" show-less-items />
        </div>
      </div>
    </div>
  </a-card>

  <!-- 设备控制弹窗 -->
  <a-modal v-model:open="controlModalVisible" :title="null" :footer="null" :closable="false" width="400px" centered
    :bodyStyle="{ padding: 0 }" wrapClassName="device-control-modal">
    <div class="control-modal-container">
      <!-- 头部区域 -->
      <div class="control-header">
        <div class="header-title">{{ currentControlDevice?.name || 'Device' }}</div>
        <div class="header-right" @click="closeControlModal">
          <CloseOutlined style="color: white; font-size: 18px;" />
        </div>
      </div>

      <!-- 控制内容区域 -->
      <div class="control-content">
        <!-- 中心电源按钮 -->
        <div class="power-section">
          <div class="power-button" :class="{ 'power-on': currentControlDevice?.powerOn }" @click="toggleDevicePower">
            <PoweroffOutlined style="font-size: 48px;" />
          </div>
        </div>

        <!-- 控制选项列表 -->
        <div class="control-options">
          <!-- 童锁 选项 -->
          <div class="control-option">
            <span class="option-label">Lock</span>
            <a-switch class="large-switch" v-model:checked="isLockOn" @change="handleLockChange" />
          </div>

          <!-- UV 选项 -->
          <div class="control-option">
            <span class="option-label">UV</span>
            <a-switch class="large-switch" v-model:checked="titaniumProUV" @change="handleTitaniumUVChange" />
          </div>

          <!-- Speed 选项 -->
          <div class="control-option">
            <span class="option-label">Speed</span>
            <a-select v-model:value="selectedWindSpeed" style="width: 80px" @change="handleWindSpeedChange">
              <a-select-option v-for="item in windSpeedOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 运行模式 选项 -->
          <div class="control-option">
            <span class="option-label">运行模式</span>
            <a-select v-model:value="selectedRunMode" style="width: 80px" @change="handleRunModeChange">
              <a-select-option v-for="item in runModeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 云定时 选项 -->
          <div class="control-option" @click="openTimerModal">
            <span class="option-label">云定时</span>
            <a-button type="text" class="timer-button">
              <RightOutlined style="font-size: 18px;" />
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 定时管理弹窗 -->
  <a-modal v-model:open="timerModalVisible" :title="null" :footer="null" :closable="false" width="400px" centered
    :bodyStyle="{ padding: 0 }" wrapClassName="timer-modal">
    <div class="timer-modal-container">
      <!-- 头部区域 -->
      <div class="timer-header">
        <div class="header-left" @click="closeTimerModal">
          <LeftOutlined style="color: white; font-size: 18px;" />
        </div>
        <div class="header-title">定时</div>
        <div class="header-right"></div>
      </div>

      <!-- 内容区域 -->
      <div class="timer-content">
        <!-- 提示信息 -->
        <div class="timer-tip">
          定时可能会存在30秒左右误差
        </div>

        <!-- 定时列表 -->
        <div class="timer-list">
          <div v-for="timer in timerList" :key="timer.id" class="timer-item">
            <div class="timer-info" @click="openTimerDetailModal(timer)">
              <div class="timer-time">{{ timer.time }}</div>
              <div class="timer-details">
                <span class="timer-repeat">{{ timer.repeatLabel }}</span>
                <span class="timer-action">{{ timer.actionLabel }}</span>
              </div>
            </div>
            <div class="timer-controls">
              <span class="timer-delete" @click="deleteTimer(timer.id)">删除</span>
              <a-switch v-model:checked="timer.enabled" size="small" @change="() => handleTimerEnableChange(timer)" />
            </div>
          </div>
        </div>

        <!-- 添加定时按钮 -->
        <div class="add-timer-section">
          <a-button type="primary" block @click="openTimerDetailModal" class="add-timer-btn">
            添加定时
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 定时详情弹窗（添加/编辑） -->
  <a-modal
    v-model:open="timerDetailModalVisible"
    title="定时设置"
    :footer="null"
    :closable="true"
    width="400px"
    centered
    :bodyStyle="{ padding: 0 }"
    wrapClassName="timer-detail-modal"
    :maskClosable="true"
    @cancel="forceCloseModal"
    @ok="forceCloseModal"
    :destroyOnClose="true">
    <div class="timer-detail-container">
      <!-- 内容区域 -->
      <div class="timer-detail-content">
        <!-- 时间选择器 -->
        <div class="time-picker-section">
          <div class="time-picker-container">
            <div class="time-column">
              <div class="time-label">小时</div>
              <a-select v-model:value="currentEditTimer.hour" style="width: 80px" @change="updateTime">
                <a-select-option v-for="hour in 24" :key="hour - 1" :value="hour - 1">
                  {{ String(hour - 1).padStart(2, '0') }}
                </a-select-option>
              </a-select>
            </div>
            <div class="time-column">
              <div class="time-label">分钟</div>
              <a-select v-model:value="currentEditTimer.minute" style="width: 80px" @change="updateTime">
                <a-select-option v-for="minute in 60" :key="minute - 1" :value="minute - 1">
                  {{ String(minute - 1).padStart(2, '0') }}
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>

        <!-- 设置选项 -->
        <div class="timer-settings">
          <!-- 重复设置 -->
          <div class="setting-item" @click="showRepeatOptions">
            <span class="setting-label">重复</span>
            <div class="setting-value">
              <span>{{ currentEditTimer?.repeatLabel }}</span>
              <RightOutlined style="margin-left: 8px; font-size: 12px; color: #999;" />
            </div>
          </div>

          <!-- 备注设置 -->
          <div class="setting-item">
            <span class="setting-label">备注</span>
            <div class="setting-value">
              <a-input v-model:value="currentEditTimer.note" placeholder="添加备注" style="border: none; padding: 0;" />
              <RightOutlined style="margin-left: 8px; font-size: 12px; color: #999;" />
            </div>
          </div>

          <!-- 执行通知 -->
          <div class="setting-item">
            <span class="setting-label">执行通知</span>
            <div class="setting-value">
              <a-switch v-model:checked="currentEditTimer.notification" />
            </div>
          </div>

          <!-- 开关控制 -->
          <div class="setting-item">
            <span class="setting-label">开关</span>
            <div class="setting-value">
              <a-switch v-model:checked="currentEditTimer.enabled" />
            </div>
          </div>
        </div>

        <div style="display: flex; justify-content: center; margin-top: 10px; cursor: pointer;"
         @click="saveTimer">
          <span >保存</span>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 重复选项弹窗 -->
  <a-modal
    v-model:open="repeatOptionsModalVisible"
    title="重复"
    :footer="null"
    :closable="true"
    width="300px"
    centered
    :bodyStyle="{ padding: '20px' }"
    wrapClassName="repeat-options-modal"
    :maskClosable="true"
    @cancel="closeRepeatOptionsModal"
    :destroyOnClose="true">

    <div class="repeat-options-container">
      <!-- 不勾选将默认只执行一次 -->
      <div class="repeat-tip">
        不勾选将默认只执行一次
      </div>

      <!-- 星期选择列表 -->
      <div class="days-list">
        <div
          v-for="(day, index) in dayNames"
          :key="index"
          class="day-item"
          @click="toggleDay(index)">
          <span class="day-name">{{ day }}</span>
          <div class="day-checkbox" :class="{ active: selectedDays[index] }">
            <div v-if="selectedDays[index]" class="checkbox-inner"></div>
          </div>
        </div>
      </div>

      <!-- 确认按钮 -->
      <div class="repeat-actions">
        <a-button type="primary" block @click="confirmRepeatOptions">
          确认
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
// Vue 3 Composition API 导入
import { ref, computed, onMounted, onUnmounted, defineEmits, defineExpose } from 'vue';
import { useRouter } from 'vue-router';
// Ant Design Vue 组件和图标导入
import { PoweroffOutlined, DeleteOutlined, CloseOutlined, RightOutlined, LeftOutlined } from '@ant-design/icons-vue';
import { Pagination, Button } from 'ant-design-vue';
import { message } from 'ant-design-vue';
// API 接口导入
import { getDeviceList, batchDeleteDevices, command, getCurrentWeather, getDeviceSignal, getDevicesStatus, getDeviceTimer, addDeviceTimer, deleteDeviceTimer, updateDeviceTimerState } from '../api/deviceManagement.api';

/**
 * 设备数据接口定义
 */
interface Device {
  id: string;           // 设备ID
  name: string;         // 设备名称
  locationName: string; // 位置名称
  productName: string;  // 产品名称
  icon: string;         // 设备图标
  status: string;       // 设备状态
  lat: string;          // 设备纬度
  lon: string;          // 设备经度
  powerOn?: boolean;    // 电源状态（可选）
  speed?: string;       // 风速设置（可选）
  lock?: boolean;       // 童锁状态（可选）
  uv?: boolean;         // UV状态（可选）
  activeTime?: string;  // 活跃时间（可选）
  category?: string;    // 设备类别（可选）
  createTime?: string;  // 创建时间（可选）
  updateTime?: string;  // 更新时间（可选）
  environment?: string; // 环境（室内/室外）
  isOnline?: boolean;   // 在线状态
  dataPublication?: boolean; // 数据发布状态
  mode?: string;        // 设备模式
}

/**
 * 信号数据接口定义
 */
interface SignalData {
  signalLevel: string;
  indicatorType: string;
  signal: number;
  eventTime: number;
}

/**
 * 定时项目接口定义
 */
interface TimerItem {
  id: string;
  time: string;        // 时间格式 "HH:mm"
  hour: number;        // 小时
  minute: number;      // 分钟
  repeat: string;      // 重复模式：'once' | 'daily' | 'weekly' | 'custom'
  repeatLabel: string; // 重复模式显示文本
  enabled: boolean;    // 是否启用
  action: string;      // 执行动作：'on' | 'off'
  actionLabel: string; // 动作显示文本
  note: string;        // 备注
  notification: boolean; // 是否通知
  originalData?: any;  // 保留原始API数据
}

interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

interface DeviceListResult {
  records: Device[];
  total: number;
  size: number;
  current: number;
  pages: number;
  orders: any[];
  optimizeCountSql: boolean;
  searchCount: boolean;
  maxLimit: number | null;
  countId: string | null;
}

// 路由实例
const router = useRouter();

// 定义事件
const emit = defineEmits(['deviceSelected']);

// ===== 响应式数据定义 =====
const devices = ref<Device[]>([]);        // 设备列表数据
const loading = ref(false);               // 加载状态
const pageInput = ref<number>(1);         // 跳转页码输入
const selectedRowKeys = ref<string[]>([]); // 选中的行键值
const selectedDeviceId = ref<string>(''); // 当前选中的设备ID
const currentPage = ref(1);               // 当前页码
const pageSize = ref(20);                 // 每页显示数量
const total = ref(0);                     // 总数据量

const controlModalVisible = ref(false);    // 控制弹窗可见性
const currentControlDevice = ref<Device | null>(null); // 当前控制的设备
const titaniumProUV = ref(false);          // Titanium Pro UV 状态
const selectedWindSpeed = ref('1');
const isLockOn = ref(false);   // 新增 童锁开关状态
const searchKeyword = ref('');

// 新增：运行模式和定时相关状态
const selectedRunMode = ref('auto');        // 运行模式
const timerModalVisible = ref(false);       // 定时管理弹窗可见性
const timerDetailModalVisible = ref(false); // 定时详情弹窗可见性
const timerList = ref<TimerItem[]>([]);     // 定时列表
const currentEditTimer = ref<TimerItem | null>(null); // 当前编辑的定时项
const isEditingTimer = ref(false);          // 是否为编辑模式
const repeatOptionsModalVisible = ref(false); // 重复选项弹窗可见性
const selectedDays = ref<boolean[]>([false, false, false, false, false, false, false]); // 选中的日期（周日到周六）

// ===== 过滤相关 =====
// 过滤条件
const filters = ref({
  environment: null as string | null,
  connection: null as boolean | null,
  data_publication: null as boolean | null,
  mode: null as string | null,
});

// 重置所有过滤器并重新获取数据
const resetAllFilters = async () => {
  // 重置所有过滤条件
  filters.value = {
    environment: null,
    connection: null,
    data_publication: null,
    mode: null
  };

  // 重置页码
  currentPage.value = 1;

  // 重新获取数据
  await fetchDeviceList();
};

/**
 * 获取设备列表数据
 */
const fetchDeviceList = async () => {
  try {
    loading.value = true;
    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      environment: filters.value.environment,
      isOnline: filters.value.connection,
      dataPublication: filters.value.data_publication,
      mode: filters.value.mode,
    };

    const response = await getDeviceList(params) as unknown as DeviceListResult;
    if (response) {
      // 处理设备状态
      const processedDevices = (response.records || []).map(device => {
        let powerOn = false;
        let speed = '1';
        let lock = false;
        let uv = false;

        if (device.status) {
          try {
            const statusArray = JSON.parse(device.status);
            statusArray.forEach((item: { code: string; value: any }) => {
              switch (item.code) {
                case 'switch':
                  powerOn = item.value;
                  break;
                case 'speed':
                  speed = item.value;
                  break;
                case 'lock':
                  lock = item.value;
                  break;
                case 'uv':
                  uv = item.value;
                  break;
              }
            });
          } catch (error) {
            console.error('解析设备状态失败:', error);
          }
        }

        return {
          ...device,
          powerOn,
          speed,
          lock,
          uv,
          isOnline: String(device.isOnline).toLowerCase() === 'true',
          locationName: device.locationName || '',
          key: device.id // 添加 key 属性
        };
      });

      console.log('Processed devices:', processedDevices);
      devices.value = processedDevices;
      total.value = response.total || 0;
      currentPage.value = response.current;
      pageSize.value = response.size;
    }
  } catch (error) {
    console.error('获取设备列表错误:', error);
    message.error('获取设备列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 处理搜索
 * @param value 搜索关键词
 */
const handleSearch = (value: string) => {
  searchKeyword.value = value;
  currentPage.value = 1; // 重置到第一页
  fetchDeviceList();
};

/**
 * 处理在线状态变化
 * @param status 在线状态
 */
const handleOnlineStatusChange = (status: boolean | null) => {
  filters.value.connection = status;
  currentPage.value = 1; // 重置到第一页
  fetchDeviceList();
};

/**
 * 处理环境过滤
 * @param value 环境值
 */
const handleEnvironmentChange = (value: string | null) => {
  filters.value.environment = value;
  currentPage.value = 1;
  fetchDeviceList();
};

/**
 * 处理数据发布过滤
 * @param value 数据发布状态
 */
const handleDataPublicationChange = (value: boolean | null) => {
  filters.value.data_publication = value;
  currentPage.value = 1;
  fetchDeviceList();
};

/**
 * 处理模式过滤
 * @param value 模式值
 */
const handleModeChange = (value: string | null) => {
  filters.value.mode = value;
  currentPage.value = 1;
  fetchDeviceList();
};

/**
 * 表格列配置
 */
const columns = [
  {
    title: 'Devices',
    dataIndex: 'name',
    key: 'name',
    width: 210
  },
  {
    title: 'Power',
    dataIndex: 'power',
    key: 'power',
    width: 90
  }
];

/**
 * 行选择配置
 */
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (newSelectedRowKeys: string[]) => {
    selectedRowKeys.value = newSelectedRowKeys;
  }
}));

/**
 * 页码变化处理
 * @param page 新的页码
 */
const onPageChange = (page: number) => {
  currentPage.value = page;
  fetchDeviceList();
};

/**
 * 每页显示数量变化处理
 * 重置到第一页并重新获取数据
 */
const handlePageSizeChange = () => {
  currentPage.value = 1;
  fetchDeviceList();
};

/**
 * 跳转到指定页面
 * 验证输入的页码是否有效
 */
const handleJumpToPage = () => {
  if (pageInput.value >= 1 && pageInput.value <= Math.ceil(total.value / pageSize.value)) {
    currentPage.value = pageInput.value;
    fetchDeviceList();
  } else {
    message.warning('请输入有效的页码');
  }
};

// ===== 设备控制相关方法 =====
// 模式映射表（后端 -> 前端）
const modeMap = {
  "1": "manual",
  "2": "auto",
  "3": "sleep"
};
/**
 * 发送设备控制命令
 * @param deviceId 设备ID
 * @param on 电源状态
 */
const sendDeviceCommand = async (deviceId: string, on: boolean) => {
  try {
    const params = { id: deviceId };
    const data = [{ code: "switch", value: on }];
    command(params, data);

    return true;
  }
  catch (error: any) {
    throw new Error(error.message || '设备控制失败');
  }
};

/**
 * 切换设备电源状态
 * @param device 设备对象
 */
const togglePower = async (device: Device) => {
  if (!device || !device.id) return;
  if ((device as any)._powerLoading) return;
  (device as any)._powerLoading = true;
  try {
    const success = await sendDeviceCommand(device.id, !device.powerOn);
    console.log('success--------', success);
    if (success) {
      device.powerOn = !device.powerOn;
      message.success(`设备「${device.name}」${device.powerOn ? '开启' : '关闭'}成功`);
    }
  } catch (error: any) {
    message.error(error.message || `切换设备「${device.name}」电源失败`);
  } finally {
    (device as any)._powerLoading = false;
  }
};

/**
 * 切换设备电源状态（控制弹窗中）
 */
const toggleDevicePower = async () => {
  if (currentControlDevice.value) {
    if ((currentControlDevice.value as any)._powerLoading) return;
    (currentControlDevice.value as any)._powerLoading = true;
    try {
      const success = await sendDeviceCommand(currentControlDevice.value.id, !currentControlDevice.value.powerOn);
      if (success) {
        currentControlDevice.value.powerOn = !currentControlDevice.value.powerOn;
        message.success(`设备「${currentControlDevice.value.name}」${currentControlDevice.value.powerOn ? '开启' : '关闭'}成功`);
      }
    } catch (error: any) {
      message.error(error.message || `切换设备「${currentControlDevice.value.name}」电源失败`);
    } finally {
      (currentControlDevice.value as any)._powerLoading = false;
    }
  }
};

/**
 * 处理风速改变
 * @param value 风速值
 */
const handleWindSpeedChange = async (value: string) => {
  if (currentControlDevice.value) {
    const params = { id: currentControlDevice.value.id };
    const data = [{ code: 'speed', value }];
    try {
      await command(params, data);
    } catch (error) {
      message.error('风速设置失败');
    }
  }
};

/**
 * 处理童锁开关
 * @param value 童锁状态
 */
const handleLockChange = async (value: boolean) => {
  if (currentControlDevice.value) {
    const params = { id: currentControlDevice.value.id };
    const data = [{ code: 'lock', value }];
    try {
      await command(params, data);
      isLockOn.value = value;
      // 不弹出成功提示
    } catch (error) {
      message.error('童锁设置失败');
    }
  }
};

/**
 * 批量删除选中设备
 * 先验证是否有选中项，然后执行删除操作
 */
const handleDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('Please select at least one device to delete.');
    return;
  }

  try {
    // 调用批量删除API
    await batchDeleteDevices(selectedRowKeys.value);
    // 清空选中项
    selectedRowKeys.value = [];
    // 重新获取列表数据
    fetchDeviceList();
  } catch (error) {
    console.error('删除设备错误:', error);
    message.error('删除设备失败');
  }
};

/**
 * 跳转到设备详情页面
 * @param device 设备对象
 */
const handleDeviceDetail = (device: Device) => {
  router.push({
    path: '/deviceManagement/detail',
    query: {
      id: device.id,
      deviceType: 'WiFi'
    }
  });
};

/**
 * 选中当前设备，请求设备数据和天气数据
 * @param device 设备对象
 */
const checkedCurrentDevice = async (device: Device) => {
  console.log('选中设备:', device);

  // 设置选中的设备ID
  selectedDeviceId.value = device.id;

  try {
    // 获取天气数据
    const weatherResult = await getCurrentWeather(device.lat, device.lon);

    // 尝试获取信号数据，如果设备不支持则使用默认值
    let signalData: SignalData = {
      signalLevel: 'Unknown',
      indicatorType: 'RSSI',
      signal: 0,
      eventTime: Date.now()
    };

    try {
      const signalResult = await getDeviceSignal(device.id, device.category || 'WiFi');
      if (signalResult?.result) {
        signalData = signalResult.result;
      }
    } catch (signalError) {
      console.log('设备信号获取失败，可能不支持此功能:', signalError);
    }

    // 处理天气数据
    let weatherData: any = {};

    // 处理天气数据 - API返回的数据直接就是result内容
    if (weatherResult && weatherResult.current_weather) {
      const currentWeather = weatherResult.current_weather;
      weatherData = {
        temperature: currentWeather.temp,
        humidity: currentWeather.humidity,
        condition: currentWeather.condition,
        realFeel: currentWeather.real_feel,
        windSpeed: currentWeather.wind_speed,
        pressure: currentWeather.pressure,
        uvi: currentWeather.uvi
      };
    }

    // 处理设备数据
    const deviceData = {
      id: device.id,
      name: device.name,
      locationName: device.locationName,
      productName: device.productName,
      // 添加空气质量数据
      aqi: weatherResult?.air_quality?.aqi,
      pm25: weatherResult?.air_quality?.pm25,
      temperature: weatherData.temperature,
      humidity: weatherData.humidity,
      // 添加信号数据
      signalLevel: signalData.signalLevel,
      indicatorType: signalData.indicatorType,
      signalStrength: signalData.signal,
      signalEventTime: signalData.eventTime
    };

    // 发出设备选中事件，同时传递设备数据和天气数据
    emit('deviceSelected', deviceData, weatherData);
  } catch (error: any) {
    console.log('error--------', error);
    // 只有当天气数据获取失败时才显示错误提示
    if (error.message !== 'not support this device') {
      message.error('获取数据失败');
    }
  }
};

/**
 * 打开控制弹窗
 * @param device 设备对象
 */
const openControlModal = (device: Device) => {
  currentControlDevice.value = device;
  // 初始化控制面板状态
  titaniumProUV.value = device.uv || false;
  selectedWindSpeed.value = device.speed || '1';
  selectedRunMode.value = device.mode || 'auto';
  isLockOn.value = device.lock || false;
  controlModalVisible.value = true;
};

/**
 * 处理设备控制 - 获取设备状态并打开控制弹窗
 * @param device 设备对象
 */
const handleControl = async (device: Device) => {
  try {
    // 显示加载状态
    loading.value = true;

    // 调用API获取设备状态
    const response = await getDevicesStatus([device.id]);
    console.log('response', response);
    if (Array.isArray(response) && response.length > 0) {
      const deviceStatus = response[0];
      // 解析 status 数组
      let powerOn = false;
      let speed = '1';
      let mode = 'auto';
      let lock = false;
      let uv = false;

      if (Array.isArray(deviceStatus.status)) {
        deviceStatus.status.forEach(item => {
          switch (item.code) {
            case 'switch':
              powerOn = item.value;
              break;
            case 'speed':
              speed = item.value;
              break;
            case 'mode':
              mode = modeMap[item.value] || 'auto';
              break;
            case 'lock':
              lock = item.value;
              break;
            case 'uv':
              uv = item.value;
              break;
          }
        });
      }

      const updatedDevice = {
        ...device,
        powerOn,
        speed,
        mode,
        lock,
        uv
      };

      // 打开控制弹窗
      openControlModal(updatedDevice);
    } else {
      message.error('获取设备状态失败');
      // 使用原始设备信息打开弹窗
      openControlModal(device);
    }
  } catch (error) {
    console.error('获取设备状态失败:', error);
    message.error('获取设备状态失败');
    // 使用原始设备信息打开弹窗
    openControlModal(device);
  } finally {
    loading.value = false;
  }
};

/**
 * 关闭控制弹窗
 */
const closeControlModal = () => {
  controlModalVisible.value = false;
  currentControlDevice.value = null;
};

/**
 * 处理Titanium Pro UV控制
 */
const handleTitaniumUVChange = async (checked: boolean) => {
  if (currentControlDevice.value) {
    const params = { id: currentControlDevice.value.id };
    const data = [{ code: 'uv', value: checked }];
    try {
      await command(params, data);
    } catch (error) {
      message.error('Titanium Pro UV设置失败');
    }
  }
};

/**
 * 处理运行模式变更
 */
const modeReverseMap = {
  manual: "1",
  auto: "2",
  sleep: "3"
};
const handleRunModeChange = async (value: string) => {
  if (currentControlDevice.value) {
    const params = { id: currentControlDevice.value.id };
    const data = [{ code: 'mode', value: modeReverseMap[value] || "2" }];
    try {
      await command(params, data);
      selectedRunMode.value = value;
    } catch (error) {
      message.error('运行模式设置失败');
    }
  }
};

/**
 * 打开定时管理弹窗
 */
const openTimerModal = () => {
  timerModalVisible.value = true;
  // 初始化定时列表数据
  loadTimerList();
};

/**
 * 关闭定时管理弹窗
 */
const closeTimerModal = () => {
  timerModalVisible.value = false;
};

/**
 * 加载定时列表
 */
const loadTimerList = async () => {
  if (!currentControlDevice.value) {
    message.error('未选择设备');
    return;
  }

  try {
    loading.value = true;
    const response = await getDeviceTimer(currentControlDevice.value.id);
console.log('response', response);
    if (response && Array.isArray(response)) {
      // 将API返回的数据转换为组件需要的格式
      timerList.value = response.map(timer => {
        // 解析时间字符串 (HH:mm)
        const [hour, minute] = timer.time.split(':').map(Number);

        // 解析loops字符串，转换为重复模式
        // loops规则：7位数字字符串，0=关闭，1=开启
        // 位置对应：第1位=周日，第2位=周一，第3位=周二，第4位=周三，第5位=周四，第6位=周五，第7位=周六
        // 例如：0000001 表示只有周六开启
        let repeat = 'once';
        let repeatLabel = '仅限一次';

        if (timer.loops && timer.loops.length === 7) {
          const loopsArray = timer.loops.split('').map(Number);
          const activeDays = loopsArray.reduce((acc, val) => val === 1 ? acc + 1 : acc, 0);

          // 调试信息
          console.log(`定时器 ${timer.timer_id} loops解析:`, {
            loops: timer.loops,
            loopsArray,
            activeDays
          });

          if (activeDays === 7) {
            repeat = 'daily';
            repeatLabel = '每天';
          } else if (activeDays > 0) {
            repeat = 'weekly';
            // 生成具体的周几描述
            const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            const activeDayNames = loopsArray
              .map((val, index) => val === 1 ? dayNames[index] : null)
              .filter(day => day !== null);
            repeatLabel = activeDayNames.join('、');

            console.log(`周几描述:`, activeDayNames, `最终标签: ${repeatLabel}`);
          }
        }

        // 解析functions字段，确定动作类型
        let action = 'on';
        let actionLabel = '开启';

        if (timer.functions && timer.functions.length > 0) {
          const firstFunction = timer.functions[0];
          if (firstFunction.code === 'switch') {
            action = firstFunction.value ? 'on' : 'off';
            actionLabel = firstFunction.value ? '开启' : '关闭';
          }
        }

        return {
          id: timer.timer_id,
          time: timer.time,
          hour,
          minute,
          repeat,
          repeatLabel,
          enabled: timer.enable,
          action,
          actionLabel,
          note: timer.category || '',
          notification: true,
          // 保留原始数据以备后用
          originalData: timer
        };
      });

     
    } else {
      message.error('加载定时列表失败：响应格式错误');
      timerList.value = [];
    }
  } catch (error) {
   console.error('加载定时列表失败:', error);   
    timerList.value = [];
  } finally {
    loading.value = false;
  }
};

/**
 * 删除定时
 */
const deleteTimer = async (timerId: string) => {
  if (!currentControlDevice.value) {
    message.error('未选择设备');
    return;
  }
  try {
    await deleteDeviceTimer(currentControlDevice.value.id);
    // 本地移除定时项
    timerList.value = timerList.value.filter(timer => timer.id !== timerId);
    message.success('定时删除成功');
    // 可选：刷新定时列表
    await loadTimerList();
  } catch (error) {
    message.error('定时删除失败');
  }
};

/**
 * 打开定时详情弹窗（添加/编辑）
 */
const openTimerDetailModal = (timer?: TimerItem) => {
  console.log('打开定时详情弹窗:', timer ? '编辑模式' : '添加模式');

  if (timer) {
    // 编辑模式
    currentEditTimer.value = { ...timer };
    isEditingTimer.value = true;
    console.log('设置为编辑模式, isEditingTimer:', isEditingTimer.value);
  } else {
    // 添加模式
    console.log('初始化添加模式的定时器数据...');
    currentEditTimer.value = {
      id: '', // 确保ID为空字符串
      time: '12:00',
      hour: 12,
      minute: 0,
      repeat: 'once',
      repeatLabel: '仅限一次',
      enabled: true,
      action: 'on',
      actionLabel: '开启',
      note: '',
      notification: true
    };
    isEditingTimer.value = false;
    console.log('设置为添加模式完成:');
    console.log('- isEditingTimer:', isEditingTimer.value);
    console.log('- currentEditTimer.id:', currentEditTimer.value.id);
    console.log('- currentEditTimer:', currentEditTimer.value);
  }
  timerDetailModalVisible.value = true;
};

/**
 * 关闭定时详情弹窗
 */
const closeTimerDetailModal = () => {
  console.log('关闭定时详情弹窗');

  // 强制关闭 - 使用最简单直接的方法
  timerDetailModalVisible.value = false;
  repeatOptionsModalVisible.value = false;

  // 清理可能残留的遮罩层
  setTimeout(() => {
    const masks = document.querySelectorAll('.ant-modal-mask');
    if (masks.length > 0) {
      console.log('发现残留遮罩层，正在清理...');
      masks.forEach(mask => {
        (mask as HTMLElement).remove();
      });
    }

    // 恢复body样式
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }, 100);

  // 延迟重置其他状态，避免冲突
  setTimeout(() => {
    currentEditTimer.value = null;
    isEditingTimer.value = false;
    console.log('状态重置完成');
  }, 150);
};

/**
 * 强制关闭弹窗（最直接的方法）
 */
const forceCloseModal = () => {
  console.log('强制关闭弹窗');

  // 方法1：直接设置为false
  timerDetailModalVisible.value = false;
  repeatOptionsModalVisible.value = false;

  // 方法2：通过DOM直接隐藏Modal和遮罩层
  setTimeout(() => {
    // 隐藏Modal容器
    const modal = document.querySelector('.ant-modal-wrap');
    if (modal) {
      (modal as HTMLElement).style.display = 'none';
    }

    // 强制移除遮罩层
    const masks = document.querySelectorAll('.ant-modal-mask');
    masks.forEach(mask => {
      (mask as HTMLElement).remove();
    });

    // 移除Modal根容器
    const modalRoots = document.querySelectorAll('.ant-modal-root');
    modalRoots.forEach(root => {
      (root as HTMLElement).remove();
    });

    // 恢复body的overflow样式
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }, 0);

  // 方法3：重置所有状态
  setTimeout(() => {
    currentEditTimer.value = null;
    isEditingTimer.value = false;
    timerDetailModalVisible.value = false;
    repeatOptionsModalVisible.value = false;
  }, 50);
};

/**
 * 强制关闭定时详情弹窗（保留最有效的方法）
 */
const forceCloseTimerDetailModal = () => {
  console.log('强制关闭定时详情弹窗');

  // 基础状态重置
  timerDetailModalVisible.value = false;
  currentEditTimer.value = null;
  isEditingTimer.value = false;

  // 最有效的方法：使用setTimeout强制更新
  setTimeout(() => {
    timerDetailModalVisible.value = false;
    console.log('强制关闭完成，状态:', timerDetailModalVisible.value);
  }, 0);

  // 备用方法：重新赋值
  setTimeout(() => {
    const newValue = ref(false);
    timerDetailModalVisible.value = newValue.value;
  }, 50);
};

// ===== 重复选项相关 =====
/**
 * 星期名称数组
 */
const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

/**
 * 打开重复选项弹窗
 */
const openRepeatOptionsModal = () => {
  // 根据当前定时器的重复设置初始化选中状态
  if (currentEditTimer.value) {
    initSelectedDaysFromTimer();
  }
  repeatOptionsModalVisible.value = true;
};

/**
 * 关闭重复选项弹窗
 */
const closeRepeatOptionsModal = () => {
  repeatOptionsModalVisible.value = false;

  // 清理可能残留的遮罩层
  setTimeout(() => {
    const masks = document.querySelectorAll('.ant-modal-mask');
    if (masks.length > 0) {
      console.log('清理重复选项弹窗的残留遮罩层...');
      masks.forEach(mask => {
        (mask as HTMLElement).remove();
      });
    }

    // 恢复body样式
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }, 100);
};

/**
 * 切换某一天的选中状态
 */
const toggleDay = (dayIndex: number) => {
  selectedDays.value[dayIndex] = !selectedDays.value[dayIndex];
};

/**
 * 确认重复选项
 */
const confirmRepeatOptions = () => {
  if (!currentEditTimer.value) return;

  // 根据选中的天数更新定时器的重复设置
  updateTimerRepeatFromSelectedDays();

  // 关闭弹窗
  closeRepeatOptionsModal();
};

/**
 * 根据当前定时器设置初始化选中的天数
 */
const initSelectedDaysFromTimer = () => {
  if (!currentEditTimer.value) return;

  // 重置所有选择
  selectedDays.value = [false, false, false, false, false, false, false];

  if (currentEditTimer.value.repeat === 'daily') {
    // 每天：全部选中
    selectedDays.value = [true, true, true, true, true, true, true];
  } else if (currentEditTimer.value.repeat === 'weekly') {
    // 每周：根据 repeatLabel 解析选中的天数
    const label = currentEditTimer.value.repeatLabel;
    dayNames.forEach((dayName, index) => {
      if (label.includes(dayName)) {
        selectedDays.value[index] = true;
      }
    });
  }
  // once 模式保持全部未选中
};

/**
 * 根据选中的天数更新定时器的重复设置
 */
const updateTimerRepeatFromSelectedDays = () => {
  if (!currentEditTimer.value) return;

  const selectedCount = selectedDays.value.filter(Boolean).length;

  if (selectedCount === 0) {
    // 没有选中任何天：仅限一次
    currentEditTimer.value.repeat = 'once';
    currentEditTimer.value.repeatLabel = '仅限一次';
  } else if (selectedCount === 7) {
    // 选中所有天：每天
    currentEditTimer.value.repeat = 'daily';
    currentEditTimer.value.repeatLabel = '每天';
  } else {
    // 选中部分天：每周
    currentEditTimer.value.repeat = 'weekly';
    const selectedDayNames = dayNames.filter((_, index) => selectedDays.value[index]);
    currentEditTimer.value.repeatLabel = selectedDayNames.join('、');
  }
};

/**
 * 根据重复标签生成loops字符串
 */
const generateLoopsFromRepeatLabel = (repeatLabel: string): string => {
  const loops = ['0', '0', '0', '0', '0', '0', '0']; // 初始化为全0

  dayNames.forEach((dayName, index) => {
    if (repeatLabel.includes(dayName)) {
      loops[index] = '1';
    }
  });

  return loops.join('');
};

/**
 * 全局清理Modal遮罩层的方法
 */
const clearAllModalMasks = () => {
  console.log('执行全局遮罩层清理...');

  // 清理所有遮罩层
  const masks = document.querySelectorAll('.ant-modal-mask');
  console.log(`发现 ${masks.length} 个遮罩层`);
  masks.forEach((mask, index) => {
    console.log(`移除遮罩层 ${index + 1}`);
    (mask as HTMLElement).remove();
  });

  // 清理Modal容器
  const modalWraps = document.querySelectorAll('.ant-modal-wrap');
  modalWraps.forEach((wrap, index) => {
    if ((wrap as HTMLElement).style.display === 'none' ||
        !(wrap as HTMLElement).querySelector('.ant-modal')) {
      console.log(`移除空的Modal容器 ${index + 1}`);
      (wrap as HTMLElement).remove();
    }
  });

  // 清理Modal根容器
  const modalRoots = document.querySelectorAll('.ant-modal-root');
  modalRoots.forEach((root, index) => {
    if (!(root as HTMLElement).querySelector('.ant-modal-wrap')) {
      console.log(`移除空的Modal根容器 ${index + 1}`);
      (root as HTMLElement).remove();
    }
  });

  // 恢复body样式
  document.body.style.overflow = '';
  document.body.style.paddingRight = '';

  console.log('遮罩层清理完成');
};

/**
 * 完全重置Modal状态
 */
const resetModalCompletely = () => {
  console.log('完全重置Modal状态');

  // 重置所有相关状态
  timerDetailModalVisible.value = false;
  currentEditTimer.value = null;
  isEditingTimer.value = false;

  // 清除可能的事件监听器
  document.removeEventListener('keydown', handleKeydown);

  // 重新添加事件监听器
  setTimeout(() => {
    document.addEventListener('keydown', handleKeydown);
  }, 100);

  // 强制重新渲染组件的这一部分
  setTimeout(() => {
    // 创建一个新的ref来替换原有的
    const newModalVisible = ref(false);
    timerDetailModalVisible.value = newModalVisible.value;
    console.log('完全重置后状态:', timerDetailModalVisible.value);
  }, 300);
};

/**
 * 刷新页面
 */
const reloadPage = () => {
  console.log('刷新页面');
  window.location.reload();
};

/**
 * 更新时间显示
 */
const updateTime = () => {
  if (currentEditTimer.value) {
    const hour = String(currentEditTimer.value.hour).padStart(2, '0');
    const minute = String(currentEditTimer.value.minute).padStart(2, '0');
    currentEditTimer.value.time = `${hour}:${minute}`;
  }
};

/**
 * 显示重复选项
 */
const showRepeatOptions = () => {
  openRepeatOptionsModal();
};

/**
 * 显示动作选项
 */
const showActionOptions = () => {
  // 这里可以实现一个选择器弹窗，暂时简化处理
  message.info('动作选项功能待实现');
};

/**
 * 将组件定时器数据转换为API格式
 */
const convertTimerToApiFormat = (timer: TimerItem) => {
  // 生成loops字符串：7位数字，代表周日到周六
  let loops = '0000000';

  if (timer.repeat === 'daily') {
    // 每天：全部设为1
    loops = '1111111';
  } else if (timer.repeat === 'weekly') {
    // 每周：根据 repeatLabel 解析具体的日期
    loops = generateLoopsFromRepeatLabel(timer.repeatLabel);
  } else {
    // 仅限一次：全部设为0
    loops = '0000000';
  }

  // 生成当前日期字符串 (YYYYMMDD)
  const today = new Date();
  const dateStr = today.getFullYear().toString() +
                  (today.getMonth() + 1).toString().padStart(2, '0') +
                  today.getDate().toString().padStart(2, '0');

  // 构建API参数
  const apiParams = {
    category: 'category_switch',
    date: dateStr,
    enable: timer.enabled,
    functions: [
      {
        code: 'switch',
        value: timer.action === 'on' ? true : false
      }
    ],
    loops: loops,
    time: timer.time,
    timezone_id: 'Asia/Shanghai'
  };

  console.log('转换定时器数据:', {
    original: timer,
    converted: apiParams
  });

  return apiParams;
};

/**
 * 保存定时
 */
const saveTimer = async () => {
  console.log('=== 保存定时开始 ===');
  console.log('isEditingTimer.value:', isEditingTimer.value);
  console.log('currentEditTimer.value:', currentEditTimer.value);
  console.log('currentControlDevice.value:', currentControlDevice.value);
  console.log('currentEditTimer.value?.id:', currentEditTimer.value?.id);
  console.log('判断条件 - isEditingTimer.value:', isEditingTimer.value);
  console.log('判断条件 - 有ID:', !!currentEditTimer.value?.id);

  if (!currentEditTimer.value || !currentControlDevice.value) {
    message.error('缺少必要信息');
    return;
  }

  // 更新时间显示
  updateTime();

  try {
    loading.value = true;

    // 使用更可靠的方式判断是否为编辑模式
    const hasExistingId = currentEditTimer.value.id && currentEditTimer.value.id !== '';
    const isReallyEditing = isEditingTimer.value && hasExistingId;

    console.log('最终判断结果:');
    console.log('- hasExistingId:', hasExistingId);
    console.log('- isEditingTimer.value:', isEditingTimer.value);
    console.log('- isReallyEditing:', isReallyEditing);

    if (isReallyEditing) {
      console.log('=== 执行编辑模式 ===');
      // 编辑模式：暂时使用本地更新，后续可添加更新API
      const index = timerList.value.findIndex(timer => timer.id === currentEditTimer.value!.id);
      if (index !== -1) {
        timerList.value[index] = { ...currentEditTimer.value };
      }
      message.success('定时更新成功');
    } else {
      console.log('=== 执行添加模式 ===');
      // 添加模式：调用API创建新定时
      const timerParams = convertTimerToApiFormat(currentEditTimer.value);
      console.log('转换后的API参数:', timerParams);

      console.log('准备调用API: addDeviceTimer');
      console.log('设备ID:', currentControlDevice.value.id);
      console.log('定时参数:', timerParams);
      console.log('addDeviceTimer函数:', typeof addDeviceTimer);

      try {
        console.log('开始调用 addDeviceTimer API...');
        const response = await addDeviceTimer(currentControlDevice.value.id, timerParams);
        console.log('API调用成功，响应:', response);
        console.log('响应类型:', typeof response);
        console.log('响应结构:', Object.keys(response || {}));
      } catch (apiError) {
        console.error('API调用失败:', apiError);
        throw apiError; // 重新抛出错误，让外层catch处理
      }

      // 根据实际API响应格式处理
      if (response && (response.success !== false)) {
        message.success('定时添加成功');
        // 重新加载定时列表以获取最新数据
        await loadTimerList();
      } else {
        const errorMsg = (response as any)?.message || '定时添加失败';
        message.error(errorMsg);
        return;
      }
    }

    closeTimerDetailModal();
  } catch (error) {
    console.error('保存定时失败:', error);
    message.error('保存定时失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 切换定时启用/禁用状态
 */
const handleTimerEnableChange = async (timer: any) => {
  if (!currentControlDevice.value) {
    message.error('未选择设备');
    return;
  }
  try {
    await updateDeviceTimerState(currentControlDevice.value.id, {
      timer_id: timer.id,
      enable: timer.enabled
    });
    message.success(`定时${timer.enabled ? '启用' : '禁用'}成功`);
    // 可选：刷新定时列表
    await loadTimerList();
  } catch (error) {
    message.error('定时状态更新失败');
  }
};

/**
 * 获取表格行的CSS类名
 * @param record 行数据
 * @param index 行索引
 */
const getRowClassName = (record: Device, index: number) => {
  return selectedDeviceId.value === record.id ? 'table-row-selected' : '';
};

/**
 * 处理设备点击事件
 * @param device 设备对象
 */
let clickTimer: ReturnType<typeof setTimeout> | null = null;
const handleDeviceClick = (device: Device) => {
  if (clickTimer) return;

  clickTimer = setTimeout(() => {
    checkedCurrentDevice(device);
    clickTimer = null;
  }, 300);
};

/**
 * 处理设备双击事件
 * @param device 设备对象
 */
const handleDeviceDoubleClick = (device: Device) => {
  if (clickTimer) {
    clearTimeout(clickTimer);
    clickTimer = null;
  }
  handleDeviceDetail(device);
};

// ===== 键盘事件处理 =====
/**
 * 处理键盘事件
 */
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (timerDetailModalVisible.value) {
      closeTimerDetailModal();
    } else if (timerModalVisible.value) {
      closeTimerModal();
    }
  }
};

// ===== 生命周期钩子 =====
/**
 * 组件挂载时获取设备列表数据
 */
onMounted(() => {
  console.log('Component mounted, fetching device list...');
  fetchDeviceList();

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeydown);
});

/**
 * 组件卸载时清理事件监听器
 */
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

// 暴露方法给父组件
defineExpose({
  handleEnvironmentChange,
  handleOnlineStatusChange,
  handleDataPublicationChange,
  handleModeChange,
  handleSearch,
  fetchDeviceList,
  resetAllFilters,
  closeTimerDetailModal,
  forceCloseTimerDetailModal,
  timerDetailModalVisible
});

// 全局调试方法（开发环境）
if (process.env.NODE_ENV === 'development') {
  (window as any).debugTimerModal = {
    close: closeTimerDetailModal,
    forceClose: forceCloseTimerDetailModal,
    reset: resetModalCompletely,
    reload: reloadPage,
    clearMasks: clearAllModalMasks, // 新增：清理遮罩层
    getState: () => ({
      visible: timerDetailModalVisible.value,
      currentTimer: currentEditTimer.value,
      isEditing: isEditingTimer.value,
      repeatModalVisible: repeatOptionsModalVisible.value
    }),
    setState: (visible: boolean) => {
      timerDetailModalVisible.value = visible;
    },
    // 新增：检查DOM状态
    checkDOM: () => {
      const modal = document.querySelector('.timer-detail-modal');
      const antModal = document.querySelector('.ant-modal');
      const masks = document.querySelectorAll('.ant-modal-mask');
      const modalWraps = document.querySelectorAll('.ant-modal-wrap');
      return {
        timerModal: modal ? 'found' : 'not found',
        antModal: antModal ? 'found' : 'not found',
        modalStyle: modal ? (modal as any).style.display : 'N/A',
        masksCount: masks.length,
        modalWrapsCount: modalWraps.length,
        bodyOverflow: document.body.style.overflow
      };
    }
  };
}

// 风速选项
const windSpeedOptions = [
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
];

// 运行模式选项
const runModeOptions = [
  { label: '手动', value: 'manual' },
  { label: '自动', value: 'auto' },
  { label: '睡眠', value: 'sleep' }
 
];

// 重复模式选项
const repeatOptions = [
  { label: '仅限一次', value: 'once' },
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '自定义', value: 'custom' },
];
</script>

<style scoped lang="less">
.footer-pagination {
      position: absolute;
    bottom: 0;
    width: 100%;
}

/* 删除图标样式 */
.delete-icn {
  margin-left: 32px;
  cursor: pointer;
}

/* 表格单元格样式 - 防止文本换行 */
:deep(.ant-table-cell) {
  white-space: nowrap;
}

/* 设备图标样式 */
.rounded-full-img {
  width: 30px;
  height: 50px;
}

/* 分页选择区域样式 */
.selectPage {
  padding: 10px;
}

/* 电源控制区域样式 */
.controlPower {
  display: flex;
  align-items: center;
  justify-content: space-evenly;

  /* 控制按钮容器 */
  .control-div {
    width: 44px;
    height: 44px;
    border: 1px solid #E6E6E6;
    border-radius: 50%;
    cursor: pointer;
    flex-shrink: 0;

    /* 控制图标 */
    .control-img {
      width: 26px;
      height: 26px;
    }
  }
}

/* 电源开启状态按钮样式 */
.power-btn {
  background: #00aaeb;
  color: #fff;
}

/* 设备行样式 */
.device-row {
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  // &:hover {
  //   background-color: #f0f9ff;
  // }
}

/* 选中的设备行样式 */
.device-row-selected {
  background-color: #f5f9fe;
}

/* 表格行选择样式 - 统一checkbox选择和单击选择的视觉效果 */
:deep(.ant-table-row-selected) {
  background-color: #f5f9fe !important;
}

:deep(.ant-table-row-selected:hover) {
  background-color: #f5f9fe !important;
}

/* 确保选中状态下的单元格也有正确的背景色 */
:deep(.ant-table-row-selected .ant-table-cell) {
  background-color: #f5f9fe !important;
}

/* 通过rowClassName设置的整行选中样式 */
:deep(.table-row-selected) {
  background-color: #f5f9fe !important;
}

:deep(.table-row-selected:hover) {
  background-color: #f5f9fe !important;
}

:deep(.table-row-selected .ant-table-cell) {
  background-color: #f5f9fe !important;
}


/* 覆盖 ant-card-body 的默认内边距 */
:deep(.ant-card-body) {
  padding: 0;
  height: 100%;
  position: relative;
}

/* 设备控制弹窗样式 */
:deep(.device-control-modal .ant-modal-content) {
  border-radius: 20px;
  overflow: hidden;
}

.control-modal-container {
  width: 100%;
  background: #f5f5f5;
}

.control-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  cursor: pointer;
  min-width: 18px;
}

.header-left {
  justify-content: flex-start;
}

.header-right {
  justify-content: flex-end;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  flex: 1;
}

.control-content {
  padding: 30px 20px;
  background: #f5f5f5;
  min-height: 400px;
}

.power-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.power-button {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
  border: 4px solid #d9d9d9;
}

.power-button:hover {
  background: #d9d9d9;
}

.power-button.power-on {
  background: #00aaeb;
  color: white;
  border-color: #00aaeb;
}

.control-options {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-option:last-child {
  border-bottom: none;
}

.control-option:hover {
  background-color: #fafafa;
}

.option-label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.timer-button {
  padding: 0;
  height: auto;
  color: #666;
  display: flex;
  align-items: center;
}

/* 定时管理弹窗样式 */
:deep(.timer-modal .ant-modal-content) {
  border-radius: 20px;
  overflow: hidden;
}

.timer-modal-container {
  width: 100%;
  background: #f5f5f5;
}

.timer-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.timer-content {
  padding: 20px;
  background: #f5f5f5;
  min-height: 400px;
}

.timer-tip {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.timer-list {
  height: 40vh;
  overflow-y: auto;
  margin-bottom: 20px;
}

.timer-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timer-info {
  flex: 1;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 8px;
  border-radius: 6px;
}

.timer-time {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.timer-details {
  font-size: 14px;
  color: #666;
}

.timer-repeat {
  margin-right: 8px;
}

.timer-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timer-delete {
  color: #ff4d4f;
  cursor: pointer;
  font-size: 14px;
}

.add-timer-section {
  margin-top: 20px;
}

.add-timer-btn {
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
}

/* 定时详情弹窗样式 */
:deep(.timer-detail-modal .ant-modal-content) {
  border-radius: 20px;
  overflow: hidden;
}

.timer-detail-container {
  width: 100%;
  background: #f5f5f5;
}

.timer-detail-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.timer-detail-content {
  padding: 20px;
  background: #f5f5f5;
  min-height: 500px;
}

.time-picker-section {
  margin-bottom: 30px;
}

.time-picker-container {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 40px 0;
}

.time-column {
  text-align: center;
}

.time-label {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}

.timer-settings {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background-color: #fafafa;
}

.setting-label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.setting-value {
  display: flex;
  align-items: center;
  color: #666;
}

/* 大尺寸Switch样式 */
.large-switch {
  transform: scale(1.2);
  transform-origin: center;
}

/* 确保大尺寸Switch有足够的空间 */
.control-option .large-switch {
  margin: 4px 8px;
}

/* ===== 重复选项弹窗样式 ===== */
.repeat-options-container {
  .repeat-tip {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
    text-align: left;
  }

  .days-list {
    .day-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .day-name {
        font-size: 16px;
        color: #333;
      }

      .day-checkbox {
        width: 20px;
        height: 20px;
        border: 2px solid #d9d9d9;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &.active {
          border-color: #1890ff;
          background-color: #1890ff;

          .checkbox-inner {
            width: 8px;
            height: 8px;
            background-color: white;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .repeat-actions {
    margin-top: 20px;

    .ant-btn {
      height: 40px;
      font-size: 16px;
      border-radius: 8px;
    }
  }
}
</style>
