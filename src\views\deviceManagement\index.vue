<template>
  <div class="device-management">
    <div class="page-header">
      <h1 class="page-title">{{ t('routes.airgle.deviceManagement') }}</h1>
      <a-button preIcon="ant-design:plus-outlined" type="primary" class="add-device-btn" @click="showAddModal"> {{
        t('airgle.device.deviceModelAdd')
      }}</a-button>
    </div>
    <!-- 过滤器和操作区 -->
    <div class="device-management__toolbar">
      <!-- 过滤器组 -->
      <div class="filter-group">
        <template v-for="(options, key) in FILTER_OPTIONS" :key="key">
          <a-dropdown :trigger="['click']">
            <a-button class="filter-button">
              {{ t(`airgle.device.filter.${key.toLowerCase()}`) }}
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="({ key: value }) => handleFilterChange(key.toLowerCase(), value)">
                <a-menu-item v-for="option in options" :key="option.value">
                  <template #icon>
                    <CheckOutlined v-if="isFilterSelected(key.toLowerCase(), option.value)" />
                  </template>
                  {{ option.label }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
        <a-button class="reset-button" @click="handleReset">
          {{ t('common.resetText') }}
        </a-button>
      </div>

      <!-- 操作组 -->
      <div class="action-group">
        <!-- 搜索框 -->
        <a-input-search v-model:value="searchValue" class="search-input"
          :placeholder="t('airgle.device.searchPlaceholder')" @input="debounceSearch" allow-clear>
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input-search>

        <!-- 自定义按钮 -->
        <!-- <a-button
          type="primary"
          class="customize-btn"
          @click="handleCustomize"
        >

          {{ t('airgle.device.deviceCustomize') }}
        </a-button> -->

        <!-- 添加设备按钮 -->
        <!-- <a-button
          type="primary"
          class="add-btn"
          @click="handleAdd"
        >
        +
        </a-button> -->
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="device-management__status">
      <TopBar ref="topBarRef" />
    </div>

    <!-- 主内容区 -->
    <div class="device-management__content">
      <a-row :gutter="[16, 16]" style="margin-left: 0;">
        <!-- 左侧设备列表 -->
        <a-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6" style="padding-left: 0;
    padding-right: 0;
    height: 72vh;
   ">
          <leftList ref="leftListRef" @deviceSelected="handleDeviceSelected" v-loading="loading" />
        </a-col>

        <!-- 右侧图表区域 -->
        <a-col :xs="24" :sm="24" :md="16" :lg="18" :xl="18" style="height: 72vh;">
          <div v-if="!selectedDevice" class="empty-state">
            <a-empty :description="t('airgle.device.pleaseSelect')" />
          </div>
          <rightEchart style="height: 100%;background: #f5f9fe;" v-else :product-name="selectedDevice.productName"
            :location-name="selectedDevice.locationName" :device-id="selectedDevice.deviceId" />
        </a-col>
      </a-row>
    </div>

    <!-- 添加设备弹窗 -->
    <addDevice ref="addDeviceRef" @success="handleAddSuccess" />

    <!-- 添加设备弹窗 -->
    <a-modal v-model:open="addModalVisible" title="添加设备" :footer="null" width="500px" :closable="true"
      @cancel="closeAddModal">
      <div class="add-device-modal">
        <div class="modal-content">
          <div class="message">
            <Icon icon="ant-design:info-circle-outlined" :size="24" color="#1890ff" />
            <p>请下载app，根据app指引进行设备配网添加</p>
          </div>

          <div class="download-buttons">
            <a-button type="primary" size="large" class="download-btn google-btn" @click="downloadGoogleApp">
              <Icon icon="ant-design:google-outlined" :size="20" />
              Google Play
            </a-button>

            <a-button type="primary" size="large" class="download-btn apple-btn" @click="downloadAppleApp">
              <Icon icon="ant-design:apple-outlined" :size="20" />
              App Store
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useDebounce } from '@/hooks/core/useDebounce';
import {
  DownOutlined,
  SearchOutlined,
  CheckOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { FILTER_OPTIONS, DEBOUNCE_DELAY } from './constants';
import TopBar from './components/topBar.vue';
import RightEchart from './components/rightEchart.vue';
import LeftList from './components/leftList.vue';
import AddDevice from './components/add/addDevice.vue';
import Icon from '/@/components/Icon/index';

// 类型定义
interface DeviceData {
  id: string;
  productName: string;
  locationName: string;
}

interface SelectedDevice {
  productName: string;
  locationName: string;
  deviceId: string;
}

// 组件状态
const { t } = useI18n();
const loading = ref(false);
const searchValue = ref('');
const selectedDevice = ref<SelectedDevice | null>(null);
const addModalVisible = ref(false);

// 组件引用
const addDeviceRef = ref<InstanceType<typeof AddDevice> | null>(null);
const topBarRef = ref<InstanceType<typeof TopBar> | null>(null);
const leftListRef = ref<InstanceType<typeof LeftList> | null>(null);

// 当前选中的过滤器值
const currentFilters = ref<Record<string, any>>({});

// 检查过滤器是否被选中
const isFilterSelected = (type: string, value: any) => {
  return currentFilters.value[type] === value;
};

// 处理过滤器变化
const handleFilterChange = (filterType: string, value: any) => {
  if (!leftListRef.value) return;

  currentFilters.value[filterType] = value;

  switch (filterType) {
    case 'connection':
      leftListRef.value.handleOnlineStatusChange(value);
      break;
    case 'environment':
      leftListRef.value.handleEnvironmentChange(value);
      break;
    case 'data_publication':
      leftListRef.value.handleDataPublicationChange(value);
      break;
    case 'mode':
      leftListRef.value.handleModeChange(value);
      break;
  }
};

// 重置所有过滤器
const handleReset = () => {
  if (!leftListRef.value) return;

  // 清空当前过滤器状态
  currentFilters.value = {};

  // 清空搜索框
  searchValue.value = '';

  // 调用一次性重置所有过滤器
  leftListRef.value.resetAllFilters();
};

// 处理搜索
const handleSearch = (value: string) => {
  if (!leftListRef.value) return;
  leftListRef.value.handleSearch(value);
};

// 防抖处理
const { debounce } = useDebounce();
const debounceSearch = debounce((e: Event) => {
  const target = e.target as HTMLInputElement;
  handleSearch(target.value);
}, DEBOUNCE_DELAY);

// 处理设备选择
const handleDeviceSelected = (deviceData: DeviceData, weatherData: any) => {
  selectedDevice.value = {
    productName: deviceData.productName,
    locationName: deviceData.locationName,
    deviceId: deviceData.id
  };

  if (topBarRef.value) {
    topBarRef.value.updateDeviceData(deviceData);
    topBarRef.value.updateWeatherData(weatherData);
  }
};

// 处理添加设备
const handleAdd = () => {
  addDeviceRef.value?.handleOk();
};

// 处理自定义设备
const handleCustomize = () => {
  message.info('Coming soon...');
};

// 处理添加成功
const handleAddSuccess = () => {
  message.success(t('airgle.device.addSuccess'));
  leftListRef.value?.fetchDeviceList();
};

// 显示添加设备弹窗
const showAddModal = () => {
  addModalVisible.value = true;
};

// 关闭添加设备弹窗
const closeAddModal = () => {
  addModalVisible.value = false;
};

// 下载Google Play应用
const downloadGoogleApp = () => {
  // 这里可以替换为实际的Google Play链接
  window.open('https://play.google.com/store/apps/details?id=com.airgle.www&hl=en', '_blank');
};

// 下载App Store应用
const downloadAppleApp = () => {
  // 这里可以替换为实际的App Store链接
  window.open('https://apps.apple.com/us/app/airgle/id1467865598', '_blank');
};

// 生命周期钩子
onMounted(() => {
  // 初始化加载
});
</script>

<style lang="less" scoped>
.device-management {
  padding: 0px 24px;
  background-color: transparent;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }

  .add-device-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
  }

  &__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  &__status {
    margin-bottom: 16px;
  }

  &__content {
    background: none;
    padding: 0px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  }
}

.filter-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;

  .filter-button {
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reset-button {
    min-width: 80px;
  }
}

.action-group {
  display: flex;
  gap: 12px;
  align-items: center;

  .search-input {
    width: 300px;
  }

  .customize-btn {
    white-space: nowrap;
  }

  .add-btn {
    padding: 0 8px;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #fafafa;
  border-radius: 4px;
}

.add-device-modal {
  .modal-content {
    text-align: center;
    padding: 20px 0;

    .message {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;
      padding: 16px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 6px;

      p {
        margin: 0 0 0 12px;
        font-size: 16px;
        color: #52c41a;
        font-weight: 500;
      }
    }

    .download-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;

      .download-btn {
        min-width: 140px;
        height: 48px;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.3s ease;

        &.google-btn {
          background: linear-gradient(135deg, #4285f4, #34a853);
          border-color: #4285f4;

          &:hover {
            background: linear-gradient(135deg, #3367d6, #2d8f47);
            border-color: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
          }
        }

        &.apple-btn {
          background: linear-gradient(135deg, #000000, #333333);
          border-color: #000000;

          &:hover {
            background: linear-gradient(135deg, #1a1a1a, #404040);
            border-color: #1a1a1a;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .device-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }

    &__toolbar {
      flex-direction: column;
      gap: 16px;
    }

    .filter-group,
    .action-group {
      width: 100%;
    }

    .search-input {
      width: 100%;
    }
  }
}
</style>
