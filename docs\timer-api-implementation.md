# 定时器API实现文档

## 概述
成功添加了设备定时器的创建功能，实现了 POST `/airgle/aglDevices/addDeviceTimer/{device_id}` 接口的集成。

## 实现的功能

### 1. API接口定义
在 `src/views/deviceManagement/api/deviceManagement.api.ts` 中添加了：

- **接口枚举**：`addDeviceTimer = '/airgle/aglDevices/addDeviceTimer'`
- **请求参数接口**：`AddDeviceTimerParams`
- **响应结果接口**：`AddDeviceTimerResult`
- **API方法**：`addDeviceTimer(deviceId: string, params: AddDeviceTimerParams)`

### 2. 参数格式（已更新匹配实际API）
```typescript
interface AddDeviceTimerParams {
  category?: string;          // 定时任务的分类，如 "category_switch"
  date?: string;              // 执行日期，如"20250717"
  enable?: boolean;           // 定时器是否启用
  functions?: TimerFunction[]; // 定时执行的指令列表
  loops?: string;             // 7位数字字符串，代表周日到周六
  time?: string;              // 执行时间，如"16:51"
  timezone_id?: string;       // 时区ID，如"Asia/Shanghai"
}

interface TimerResponseData {
  category: string;           // 定时任务分类
  date: string;               // 执行日期
  enable: boolean;            // 是否启用
  functions: TimerFunction[]; // 执行指令列表
  loops: string;              // 循环模式
  time: string;               // 执行时间
  timer_id: string;           // 定时器ID（响应中包含）
  timezone_id: string;        // 时区ID
}
```

### 3. 数据转换逻辑
在 `leftList.vue` 中实现了 `convertTimerToApiFormat` 方法：

- **时间格式**：直接使用组件中的时间格式
- **日期格式**：自动生成当前日期的 YYYYMMDD 格式
- **循环模式**：
  - `daily`：设置为 "1111111"（每天）
  - `weekly`：设置为 "0000001"（默认周六）
  - `once`：设置为 "0000000"（仅一次）
- **执行指令**：根据开关状态生成 power 指令

### 4. 保存流程
修改了 `saveTimer` 方法：

1. **编辑模式**：继续使用本地更新（待后续添加更新API）
2. **添加模式**：
   - 调用 `convertTimerToApiFormat` 转换数据
   - 调用 `addDeviceTimer` API
   - 成功后重新加载定时列表
   - 显示成功/失败消息

## 使用示例

### API调用示例（已更新）
```javascript
const timerParams = {
  category: "category_switch",
  date: "20250718",
  enable: true,
  functions: [
    {
      code: "switch",
      value: true // 开启设备
    }
  ],
  loops: "1111111", // 每天执行
  time: "16:51",
  timezone_id: "Asia/Shanghai"
};

const response = await addDeviceTimer("device123", timerParams);
```

### 响应格式（实际API响应）
```javascript
{
  success: true,
  message: "定时任务创建成功",
  code: 200,
  result: {
    category: "category_switch",
    date: "20250717",
    enable: false,
    functions: [
      {
        code: "switch",
        value: true
      }
    ],
    loops: "0000000",
    time: "16:51",
    timer_id: "64439973",
    timezone_id: "Asia/Shanghai"
  }
}
```

## 技术特点

1. **类型安全**：使用 TypeScript 接口确保类型安全
2. **错误处理**：完整的错误处理和用户反馈
3. **数据验证**：自动转换和验证数据格式
4. **用户体验**：保存后自动刷新列表，显示操作结果

## 测试验证

- ✅ API接口定义正确
- ✅ 数据格式转换正确
- ✅ 编译无错误
- ✅ 热重载更新成功

## 更新记录

### v2.0 - 2025-01-18
- **重要更新**：根据实际API响应调整了数据格式
- 更新了 `AddDeviceTimerParams` 接口定义
- 添加了 `TimerResponseData` 接口
- 修改了数据转换逻辑：
  - `code` 字段从 "power" 改为 "switch"
  - 移除了 `alias_name` 字段
  - 添加了 `enable` 字段
  - 调整了字段顺序以匹配API
- 优化了响应数据解析，正确处理 `functions` 字段
- 改进了动作类型的判断逻辑

### 关键变更
1. **发送格式**：字段名称和结构完全匹配实际API
2. **响应处理**：正确解析 `timer_id`、`enable` 等字段
3. **动作解析**：根据 `functions[0].value` 正确判断开关状态

## 后续优化建议

1. 添加定时器更新API的支持
2. 优化循环模式的用户选择界面
3. 添加更多的执行指令类型支持
4. 增加定时任务的详细验证逻辑
5. 考虑添加定时器批量操作功能
