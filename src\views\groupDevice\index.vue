<template>
  <div class="count">
    <!-- <div class="head display-between">
      <div class="left">
        {{ t('airgle.groupDevice.deviceModel') }}
      </div>
      <div class="right">
        <a-button type="primary" @click="handleAddDevice" preIcon="ant-design:plus-outlined"> {{ t('airgle.groupDevice.createGroup') }}</a-button>
      </div>
    </div> -->
    <h1 class="page-title">{{ t('routes.airgle.group') }}</h1>
    <div class="filter">
      <!-- 过滤器组 -->
      <div class="filter-group">
        <template v-for="(options, key) in FILTER_OPTIONS" :key="key">
          <a-dropdown :trigger="['click']">
            <a-button class="filter-button">
              {{ t(`airgle.device.filter.${String(key).toLowerCase()}`) }}
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="({ key: value }) => handleFilterChange(String(key).toLowerCase(), value)">
                <a-menu-item v-for="option in options" :key="option.value">
                  <template #icon>
                    <CheckOutlined v-if="isFilterSelected(String(key).toLowerCase(), option.value)" />
                  </template>
                  {{ option.label }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
        <a-button class="reset-button" @click="handleReset">
          {{ t('common.resetText') }}
        </a-button>
      </div>
      <div class="right">
        <a-input v-model:value="searchValue" style="width: 300px"
          :placeholder="t('airgle.groupDevice.searchPlaceholder')">
          <template #prefix>
            <ZoomInOutlined />
          </template>
        </a-input>
        <a-button preIcon="ant-design:plus-outlined" class="button1"> {{ t('airgle.device.deviceCustomize')
        }}</a-button>
        <!-- <a-button @click="handleAdd" preIcon="ant-design:plus-outlined" size="large" class="button2"></a-button> -->
        <a-button type="primary" @click="handleAddDevice" preIcon="ant-design:plus-outlined"> {{
          t('airgle.groupDevice.createGroup') }}</a-button>
      </div>
    </div>
    <div class="information">
      <topBar ref="topBarRef" />
    </div>
    <!-- 主内容区 -->
    <div class="group-management__content">
      <!-- 群组详情视图（全屏） -->
      <div v-if="currentView === 'groupDetail'">
        <groupDetail :group-data="currentGroupData" @back="handleBackToGroupList" />
      </div>

      <!-- 主视图：三栏并排布局 -->
      <div v-else>
        <a-row :gutter="[16, 16]">
          <!-- 左侧：群组列表（始终显示） -->
          <a-col :xs="24" :sm="selectedGroup ? 7 : 24" :md="selectedGroup ? 6 : 24" :lg="selectedGroup ? 5 : 24"
            :xl="selectedGroup ? 5 : 24" class="grouplist">
            <div class="panel-header">
              <h4>Groups</h4>
            </div>
            <groupList ref="groupListRef" @groupSelected="handleGroupSelected"
              @showGroupDevices="handleShowGroupDevices" @showGroupDetail="handleShowGroupDetail" v-loading="loading" />
          </a-col>

          <!-- 中间：设备列表（只有选择群组后才显示） -->
          <a-col v-if="selectedGroup" :xs="24" :sm="5" :md="3" :lg="selectedDevice ? 3 : 6"
            :xl="selectedDevice ? 3 : 6" class="grouplist">
            <device-list :group-id="selectedGroup.id" @deviceSelected="handleDeviceSelected" />
          </a-col>
          <!-- 右侧：图表区域（只有选择设备后才显示） -->
          <a-col v-if="selectedDevice" :xs="24" :sm="12" :md="15" :lg="16" :xl="16">
            <div class="panel-header">
              <h4>{{ selectedDevice.productName }} Charts</h4>
            </div>
            <!-- 显示设备图表 -->
            <rightEchart :product-name="selectedDevice.productName" :location-name="selectedDevice.locationName"
              :device-id="selectedDevice.deviceId" :selected-device="selectedDevice" />
          </a-col>
        </a-row>
      </div>
    </div>
    <DeviceGroupModal ref="deviceGroupModalRef" @success="handleGroupCreated" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useRouter } from 'vue-router';
import { DownOutlined, ZoomInOutlined, CheckOutlined } from '@ant-design/icons-vue';
import { message, Pagination } from 'ant-design-vue';
import topBar from '../deviceManagement/components/topBar.vue';
import groupList from './components/groupList.vue';
import groupDetail from './components/groupDetail.vue';
import rightEchart from '../deviceManagement/components/rightEchart.vue';
import { FILTER_OPTIONS, DEBOUNCE_DELAY } from './constants';
import DeviceGroupModal from './components/DeviceGroupModal.vue';
import { getDeviceList, getCurrentWeather, getDeviceSignal } from '../deviceManagement/api/deviceManagement.api';
import deviceList from './components/deviceList.vue';

// 类型定义
interface GroupData {
  id: string;
  name: string;
  deviceCount: number;
  location: string;
  status: string;
  devices: string[];
  groupName?: string;
  workspaceName?: string;
  remark?: string;
  iconUrl?: string;
  groupType?: number;
  productId?: string;
  type?: number;
  localKey?: string;
  pv?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  sysOrgCode?: string;
}

interface DeviceData {
  id: string;
  name?: string;
  productName: string;
  locationName: string;
  deviceId: string;
  signalStrength?: number;
  signalLevel?: string;
  aqi?: number;
  temperature?: number;
  humidity?: number;
  pm25?: number;
  co2?: number;
}

interface Device {
  id: string;
  name: string;
  locationName: string;
  productName: string;
  icon: string;
  status: string;
  lat: string;
  lon: string;
  powerOn?: boolean;
  speed?: string;
  lock?: boolean;
  uv?: boolean;
  activeTime?: string;
  category?: string;
  createTime?: string;
  updateTime?: string;
  environment?: string;
  isOnline?: boolean;
  dataPublication?: boolean;
  mode?: string;
}

const { t } = useI18n();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const searchValue = ref('');
const selectedGroup = ref<GroupData | null>(null);
const selectedDevice = ref<DeviceData | null>(null);
const currentView = ref<'groupList' | 'groupDetail'>('groupList');
const currentGroupData = ref<GroupData | null>(null);
const groupListRef = ref<any>(null);
const deviceListRef = ref<any>(null);
const deviceGroupModalRef = ref<any>(null);
const topBarRef = ref<any>(null);

// 当前选中的过滤器值
const currentFilters = ref<Record<string, any>>({});

// 检查过滤器是否被选中
const isFilterSelected = (type: string, value: any) => {
  return currentFilters.value[type] === value;
};

// 处理过滤器变化
const handleFilterChange = (filterType: string, value: any) => {
  if (!groupListRef.value) return;
  currentFilters.value[filterType] = value;

  // 这里可以添加具体的过滤逻辑
  console.log('Filter changed:', filterType, value);

  switch (filterType) {
    case 'connection':
      groupListRef.value.handleOnlineStatusChange(value);
      break;
    case 'environment':
      groupListRef.value.handleEnvironmentChange(value);
      break;
    case 'data_publication':
      groupListRef.value.handleDataPublicationChange(value);
      break;
    case 'mode':
      groupListRef.value.handleModeChange(value);
      break;
  }
};

// 方法定义
// 处理群组选择（单击群组：只显示群组设备列表，不显示图表）
const handleGroupSelected = (group: GroupData) => {
  selectedGroup.value = group;
  selectedDevice.value = null; // 重置选中的设备，清除右侧图表
  console.log('Selected group for devices list:', group);

  // 刷新设备列表（暂时显示所有设备，后续可以添加群组过滤）
  if (deviceListRef.value) {
    (deviceListRef.value as any).fetchDeviceList();
  }
};

// 单击群组：显示群组设备列表（与handleGroupSelected相同）
const handleShowGroupDevices = (group: GroupData) => {
  handleGroupSelected(group);
};

// 双击群组：显示群组详情
const handleShowGroupDetail = (group: GroupData) => {
  console.log('Show group detail:', group);
  currentGroupData.value = group;
  currentView.value = 'groupDetail';
};

// 返回群组列表
const handleBackToGroupList = () => {
  currentView.value = 'groupList';
  currentGroupData.value = null;
};

// 设备列表相关的响应式数据
const devices = ref<Device[]>([]);
const deviceLoading = ref(false);
const devicePageInput = ref<number>(1);
const deviceCurrentPage = ref(1);
const devicePageSize = ref(20);
const deviceTotal = ref(0);
const deviceSearchKeyword = ref('');

// 设备列表过滤条件
const deviceFilters = ref({
  environment: null as string | null,
  connection: null as boolean | null,
  data_publication: null as boolean | null,
  mode: null as string | null,
});

// 设备列表列配置
const deviceColumns = [
  {
    title: 'Devices',
    dataIndex: 'name',
    key: 'name',
    width: 180
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 120
  }
];

// 获取设备列表数据
const fetchDeviceList = async () => {
  try {
    deviceLoading.value = true;
    const params = {
      pageNo: deviceCurrentPage.value,
      pageSize: devicePageSize.value,
      keyword: deviceSearchKeyword.value,
      environment: deviceFilters.value.environment,
      isOnline: deviceFilters.value.connection,
      dataPublication: deviceFilters.value.data_publication,
      mode: deviceFilters.value.mode,
    };

    const response = await getDeviceList(params);
    if (response) {
      // 处理设备状态
      const processedDevices = (response.records || []).map(device => {
        let powerOn = false;
        let speed = '1';
        let lock = false;
        let uv = false;

        if (device.status) {
          try {
            const statusArray = JSON.parse(device.status);
            statusArray.forEach((item: { code: string; value: any }) => {
              switch (item.code) {
                case 'switch':
                  powerOn = item.value;
                  break;
                case 'speed':
                  speed = item.value;
                  break;
                case 'lock':
                  lock = item.value;
                  break;
                case 'uv':
                  uv = item.value;
                  break;
              }
            });
          } catch (error) {
            console.error('解析设备状态失败:', error);
          }
        }

        return {
          ...device,
          powerOn,
          speed,
          lock,
          uv,
          isOnline: String(device.isOnline).toLowerCase() === 'true',
          locationName: device.locationName || '',
          key: device.id
        };
      });

      devices.value = processedDevices;
      deviceTotal.value = response.total || 0;
      deviceCurrentPage.value = response.current;
      devicePageSize.value = response.size;
    }
  } catch (error) {
    console.error('获取设备列表错误:', error);
    message.error('获取设备列表失败');
  } finally {
    deviceLoading.value = false;
  }
};

// 获取设备状态样式类
const getDeviceStatusClass = (device: Device) => {
  return {
    'status-on': device.powerOn,
    'status-off': !device.powerOn
  };
};
// 重置所有过滤器
const handleReset = () => {
  if (!groupListRef.value) return;

  // 清空当前过滤器状态
  currentFilters.value = {};

  // 清空搜索框
  searchValue.value = '';

  // 调用一次性重置所有过滤器
  groupListRef.value.resetAllFilters();
};
// 设备分页相关方法
const onDevicePageChange = (page: number) => {
  deviceCurrentPage.value = page;
  fetchDeviceList();
};

const handleDevicePageSizeChange = () => {
  deviceCurrentPage.value = 1;
  fetchDeviceList();
};

const handleDeviceJumpToPage = () => {
  if (devicePageInput.value >= 1 && devicePageInput.value <= Math.ceil(deviceTotal.value / devicePageSize.value)) {
    deviceCurrentPage.value = devicePageInput.value;
    fetchDeviceList();
  } else {
    message.warning('请输入有效的页码');
  }
};

// 设备点击事件处理
const handleDeviceClick = async (device: Device) => {
  try {
    // 获取天气数据
    const weatherResult = await getCurrentWeather(device.lat, device.lon);

    // 尝试获取信号数据
    let signalData = {
      signalLevel: 'Unknown',
      indicatorType: 'RSSI',
      signal: 0,
      eventTime: Date.now()
    };

    try {
      const signalResult = await getDeviceSignal(device.id, device.category || 'WiFi');
      if (signalResult?.result) {
        signalData = signalResult.result;
      }
    } catch (signalError) {
      console.log('设备信号获取失败，可能不支持此功能:', signalError);
    }

    // 处理天气数据
    let weatherData: any = {};
    if (weatherResult && weatherResult.current_weather) {
      const currentWeather = weatherResult.current_weather;
      weatherData = {
        temperature: currentWeather.temp,
        humidity: currentWeather.humidity,
        condition: currentWeather.condition,
        realFeel: currentWeather.real_feel,
        windSpeed: currentWeather.wind_speed,
        pressure: currentWeather.pressure,
        uvi: currentWeather.uvi
      };
    }

    // 处理设备数据
    const deviceData: DeviceData = {
      id: device.id,
      deviceId: device.id,
      name: device.name,
      locationName: device.locationName,
      productName: device.productName,
      aqi: weatherResult?.air_quality?.aqi,
      temperature: weatherData.temperature,
      humidity: weatherData.humidity,
      pm25: weatherResult?.air_quality?.pm25,
      co2: weatherResult?.air_quality?.co2,
      signalStrength: signalData.signal
    };

    handleDeviceSelected(deviceData, weatherData);
  } catch (error: any) {
    if (error.message !== 'not support this device') {
      message.error('获取数据失败');
    }
  }
};

// 设备双击事件处理
const handleDeviceDoubleClick = (device: Device) => {
  router.push({
    path: '/deviceManagement/detail',
    query: {
      id: device.id,
      deviceType: 'WiFi'
    }
  });
};

const handleAdd = () => {
  // 处理其他添加操作
};

const handleAddDevice = () => {
  if (deviceGroupModalRef.value) {
    (deviceGroupModalRef.value as any).visible = true;
  }
};

const handleGroupCreated = (groupData: any) => {
  console.log('Group created:', groupData);
  message.success('Group created successfully!');
  // 延迟刷新分组列表
  setTimeout(() => {
    if (groupListRef.value) {
      (groupListRef.value as any).refreshList();
    }
  }, 500); // 延迟 0.5 秒
};

// 监听选中群组变化，刷新设备列表
watch(() => selectedGroup.value, () => {
  if (selectedGroup.value) {
    deviceCurrentPage.value = 1;
    fetchDeviceList();
  }
});

// 处理设备选择（在设备列表视图中）
const handleDeviceSelected = (deviceData: DeviceData, weatherData?: any) => {
  console.log('Device selected:', deviceData);
  selectedDevice.value = {
    ...deviceData,
    deviceId: deviceData.id
  };

  // 同步更新topBar数据
  if (topBarRef.value) {
    // 更新设备数据
    (topBarRef.value as any).updateDeviceData({
      aqi: deviceData.aqi,
      temperature: deviceData.temperature,
      humidity: deviceData.humidity,
      pm25: deviceData.pm25,
      co2: deviceData.co2,
      locationName: deviceData.locationName
    });

    // 如果有天气数据，也更新天气信息
    if (weatherData) {
      (topBarRef.value as any).updateWeatherData({
        temperature: weatherData.temperature,
        humidity: weatherData.humidity,
        location: deviceData.locationName,
        condition: weatherData.condition
      });
    }
  }
};
</script>
<style lang="less" scoped>
.count {
  padding: 0px 24px;
  box-sizing: border-box;

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
  }

  .ant-dropdown-link {
    margin-right: 20px;
    color: #000;
  }

  .finally {
    margin-right: 20px;
  }

  .filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;

    .left {
      display: flex;
      align-items: center;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 10px;

      .button1 {
        margin: 0 10px;
      }

      .button2 {
        width: 80px;
      }
    }
  }

  .information {
    margin: 10px 0 5px 0;
  }
}

.group-management__content {
  margin-top: 16px;
}
.filter-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;

  .filter-button {
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reset-button {
    min-width: 80px;
  }
}
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.empty-state-small {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.panel-header {
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}

.device-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1890ff;
  font-size: 14px;

  &:hover {
    color: #40a9ff;
  }
}

.group-info {
  text-align: right;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .device-count {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

// 设备列表相关样式
.device-list-card {
  height: 100%;

  // :deep(.ant-card-body) {
  //   height: 100%;
  // }
}
:deep(.grouplist .ant-card-body) {
  padding: 0px !important;
}

.device-list-header {
  padding: 14px;
  border-bottom: 1px solid #f0f0f0;

  .total-devices {
    font-size: 14px;
    color: #262626;

    .count {
      color: #1890ff;
      margin-left: 4px;
    }
  }
}

.device-list {
  overflow-y: auto;
  padding: 8px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  &:hover {
    background-color: #f5f5f5;
  }

  &.device-item-selected {
    background-color: #e6f7ff;
    border-color: #1890ff;
  }

  .device-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .device-info {
    flex: 1;

    .device-name {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }

    .device-status {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}
</style>
