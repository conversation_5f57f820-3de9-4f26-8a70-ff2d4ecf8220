# 重复选项功能实现文档

## 功能概述
实现了定时器的重复选项功能，用户可以选择周日到周六的任意组合来设置定时器的重复执行。

## 实现的功能

### 1. 重复选项弹窗
- **触发方式**：点击定时详情弹窗中的"重复"设置项
- **弹窗内容**：
  - 提示文字："不勾选将默认只执行一次"
  - 周日到周六的选择列表
  - 每个选项都有圆形选择按钮
  - 确认按钮

### 2. 交互功能
- **选择切换**：点击任意星期选项可以切换选中/未选中状态
- **状态同步**：根据当前定时器的重复设置初始化选中状态
- **智能识别**：
  - 未选中任何天 → "仅限一次"
  - 选中所有天 → "每天"
  - 选中部分天 → "周一、周三、周五"等具体显示

### 3. 数据转换
- **前端显示** ↔ **API格式**：
  - 前端：`repeatLabel: "周一、周三、周五"`
  - API：`loops: "0101010"`（7位数字，对应周日到周六）

## 技术实现

### 1. 状态管理
```javascript
const repeatOptionsModalVisible = ref(false); // 弹窗可见性
const selectedDays = ref<boolean[]>([false, false, false, false, false, false, false]); // 选中状态
const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']; // 星期名称
```

### 2. 核心方法

#### 打开弹窗
```javascript
const openRepeatOptionsModal = () => {
  if (currentEditTimer.value) {
    initSelectedDaysFromTimer(); // 初始化选中状态
  }
  repeatOptionsModalVisible.value = true;
};
```

#### 状态初始化
```javascript
const initSelectedDaysFromTimer = () => {
  selectedDays.value = [false, false, false, false, false, false, false];
  
  if (currentEditTimer.value.repeat === 'daily') {
    selectedDays.value = [true, true, true, true, true, true, true];
  } else if (currentEditTimer.value.repeat === 'weekly') {
    const label = currentEditTimer.value.repeatLabel;
    dayNames.forEach((dayName, index) => {
      if (label.includes(dayName)) {
        selectedDays.value[index] = true;
      }
    });
  }
};
```

#### 确认选择
```javascript
const confirmRepeatOptions = () => {
  updateTimerRepeatFromSelectedDays(); // 更新定时器设置
  closeRepeatOptionsModal(); // 关闭弹窗
};
```

#### 数据转换
```javascript
const updateTimerRepeatFromSelectedDays = () => {
  const selectedCount = selectedDays.value.filter(Boolean).length;
  
  if (selectedCount === 0) {
    currentEditTimer.value.repeat = 'once';
    currentEditTimer.value.repeatLabel = '仅限一次';
  } else if (selectedCount === 7) {
    currentEditTimer.value.repeat = 'daily';
    currentEditTimer.value.repeatLabel = '每天';
  } else {
    currentEditTimer.value.repeat = 'weekly';
    const selectedDayNames = dayNames.filter((_, index) => selectedDays.value[index]);
    currentEditTimer.value.repeatLabel = selectedDayNames.join('、');
  }
};
```

#### API格式转换
```javascript
const generateLoopsFromRepeatLabel = (repeatLabel: string): string => {
  const loops = ['0', '0', '0', '0', '0', '0', '0'];
  
  dayNames.forEach((dayName, index) => {
    if (repeatLabel.includes(dayName)) {
      loops[index] = '1';
    }
  });
  
  return loops.join('');
};
```

### 3. 样式设计
- **弹窗样式**：300px宽度，居中显示
- **选项列表**：每个选项占一行，左侧文字右侧圆形按钮
- **选中状态**：蓝色圆形按钮，内部白色小圆点
- **交互反馈**：悬停时背景色变化

## 使用流程

### 1. 用户操作流程
1. 点击"添加定时"或编辑现有定时
2. 在定时详情弹窗中点击"重复"选项
3. 在重复选项弹窗中选择需要的星期
4. 点击"确认"按钮
5. 返回定时详情弹窗，显示更新后的重复设置

### 2. 数据流转流程
1. 用户选择 → `selectedDays` 数组更新
2. 确认选择 → 更新 `currentEditTimer.repeatLabel`
3. 保存定时 → 转换为API格式的 `loops` 字符串
4. API响应 → 解析 `loops` 字符串为显示格式

## 测试用例

### 1. 基础功能测试
- [ ] 点击"重复"选项能正常打开弹窗
- [ ] 点击星期选项能切换选中状态
- [ ] 点击"确认"能正常关闭弹窗并更新显示

### 2. 状态同步测试
- [ ] 新建定时：默认显示"仅限一次"
- [ ] 编辑定时：正确显示当前的重复设置
- [ ] 选择全部：显示"每天"
- [ ] 选择部分：显示具体的星期组合

### 3. 数据转换测试
- [ ] "仅限一次" → `loops: "0000000"`
- [ ] "每天" → `loops: "1111111"`
- [ ] "周一、周三、周五" → `loops: "0101010"`

## 注意事项

1. **数组索引对应**：`selectedDays[0]` 对应周日，`selectedDays[6]` 对应周六
2. **API格式**：`loops` 字符串的第1位对应周日，第7位对应周六
3. **显示格式**：中文星期名称，用顿号分隔
4. **状态管理**：确保弹窗关闭时状态正确重置

## 后续优化建议

1. 添加"全选"和"全不选"快捷按钮
2. 支持连续选择（如"工作日"、"周末"）
3. 添加选择预设（如"每个工作日"）
4. 优化移动端的触摸体验
